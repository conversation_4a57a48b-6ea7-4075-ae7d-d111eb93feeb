# Open API generator setup

This project uses Open API generator to generate API client code(ApiClass, RequestClass,
ResponseClass) from the Open API specification.

## Setup Open API generator

### Add `.yaml` to folder `spec` in the module directory

- Add the Open API specification file to the project. The file should be end by `.yaml` and should
  be placed in the module directory of the project
- Eg: `feature/YOUR_MODULE/spec/yourOpenapi.yaml`

### Feature module
Feature plugin has include openApiGenerate plugin, so you don't need to declare openApi plugin in `build.gradle` file
```toml
[versions]
openApi = "7.7.0"
localPlugin = "1.0.0"

[plugins]
openapi-generator = { id = "org.openapi.generator", version.ref = "openApi" }
local-ibank-feature = { id = "ibank.feature", version.ref = "localPlugin" }
```

#### Config `build.gradle` file

- Add feature plugin to generator plugin to `build.gradle` file

```groovy
plugins {
    alias(libs.plugins.local.ibank.feature)
}
```

### Other module

#### Declared openApi plugin in `libs.version.toml`

```toml
[versions]
localPlugin = "1.0.0"
openApi = "7.7.0"

[plugins]
openapi-generator = { id = "org.openapi.generator", version.ref = "openApi" }
local-android-openapi-generate = { id = "sdk.openapi.generate", version.ref = "localPlugin" }
```

#### Config `build.gradle` file

- Add Open API generator plugin to `build.gradle` file

```groovy
plugins {
    alias(libs.plugins.local.android.openapi.generate)
}
```