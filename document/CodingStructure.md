# Coding Structure

## Clean Architecture

Clean Architecture is actually an important concept for all software developers, not just Android developers. It is a principle that we must apply in all programming languages, in all the codes.

![appStructure.webp](/picture/appStructure.webp)

### Layers
- [**Presentation**](/document/Presentation.md): Layers that interact with the user.
- **Domain**:  Models (entities containing data) are included in this layer. The business logic of the project is kept in this layer along with the Use cases.
- **Data**: Contains data. API interfaces, databases, and Repository Implementation are included this layer.

### Data Layer
- Handling data flow and retrieving information from diverse sources like databases, web services, and files
- Manages data persistence and facilitates data access.
- APi classes, Request Classes, Response Class is auto generated by Open API generator.
- Repository classes:
  - Responsible for managing the flow of data between the domain and data layers. They abstract the source of data from the rest of the application, allowing the domain layer to interact with data without knowing where it comes from.
  - Declared in this layer end with the suffix `Repository`.
  - Should be responsible for handling data operations, such as fetching data from a network or database, and returning the `NetworkResult` to the domain layer.
- NetworkResult is a sealed class that represents the possible outcomes of a network operation. It can be one of the following:
    - Success: The operation was successful, and the result is returned.
    - Error: The operation failed, and an error message is returned. 
- Example:
    ```kotlin
    class UserRepository(private val userApi: UserApi) {
        suspend fun getUsers(): NetworkResult<List<User>> {
            return userApi.getUsers()
        }
    }
    ```

### Domain Layer
- Contains the business logic of the application, which is independent of the presentation layer. This layer is responsible for managing the flow of data between the presentation and data layers.
- Business class declared in this layer end with the suffix `UseCase`. These classes are responsible for executing a single task, such as fetching data from a repository or saving data to a database.
- DomainResult is a sealed class that represents the possible outcomes of a UseCase. It can be one of the following:
    - Success: The operation was successful, and the result is returned.
    - Error: The operation failed, and an error message is returned.
- Example:
    ```kotlin
    class GetUsersUseCase(private val userRepository: UserRepository) {
        suspend operator fun invoke(): DomainResult<User> {
            val networkResult = userRepository.getUsers()
            return networkResult.convertToDomainResult()
        } 
    }
    ```

### Presentation Layer
- The presentation layer serves as the interface between the user and the application, handling user input and displaying the corresponding outcomes. This layer usually consists of a graphical user interface, like a web page, mobile app, or desktop application
- Views, represented by Activities or Fragments, should strive to remain simple and devoid of business logic. If any business logic exists within the views, it becomes essential to refactor these classes accordingly.
- Example:
    ```kotlin
    class UserViewModel(private val getUsersUseCase: GetUsersUseCase) : ViewModel() {
        private val _users = MutableStateFlow<List<User>>()
        val users: StateFlow<List<User>> = _users

        fun getUsers() {
            viewModelScope.launch {
                val result = getUsersUseCase()
                if (result is DomainResult.Success) {
                    _users.value = result.data
                } else {
                    // Handle error
                }
            }
        }
    }
    ```
  
### Convert Network Model to Domain Model
#### Network Model
- Is response return by API.
- Auto generate by Open API generator.
#### Domain Model
- Is model that use in Domain Layer.
- Convert from Network Model.
- Declared in package `domain.model`.
- Name of Domain Model is maybe same with Network Model but end with `DMO`.
#### Convert`NetworkResult<Obj>` to `DomainResult<ObjDMO>`
  ```kotlin
    // User class auto generate by Open API generator
    class User(
      @SerializedName("id")val id: Int,
      @SerializedName("name")val name: String,
      @SerializedName("email")val email: String
    )
    // UserDMO class in domain.model package
    class UserDMO(
      @SerializedName("id")val id: Int,
      @SerializedName("name")val name: String,
      @SerializedName("email")val email: String
    )
    
    val networkResult : NetworkResult<User> = userRepository.getUsers()
    
    // fun <F, T> NetworkResult<F>.convert(cls: Class<T>) : DomainResult<T>
    val domainResult = networkResult.convert(UserDMO::class.java)
    
    // fun <F, T> NetworkResult<F>.convert(cls: Class<T>, proceed: ((from: F?, to: T) -> Unit)) : DomainResult<T>
    val domainResult = networkResult.convert(UserDMO::class.java) { from, to ->
        // Process after convert to.name = from.name.toUpperCase()
    }
    
    // fun <F, T> NetworkResult<F>.convert(convert: F.() -> T): DomainResult<T> 
    val domainResult = networkResult.convert { 
        UserDMO(
          id = it.id,
          name = it.name,
          email = it.email
        )
    }
  ```
### Using Domain Model in Presentation Layer
You can use directly `Domain Model` in Presentation Layer. But in some case you need more field for display state in UI. You can create `Presentation Model`  in Presentation Layer and add more field.
#### Presentation Model
Is class that extends `BaseUIModel<T>`, contains `Domain Model` and add more field for display state in UI.
- `BaseUIModel<T>` with T is name of Domain Model.
- Name is end with suffix `UI`. eg: `UserUI`
- Declared in package `ui.{prenenentation_name}.model`.
- user `data` field to access Domain Model.
- Example:
  ```kotlin
  class UserUI(userDMO : UserDMO) : BaseUIModel<UserDMO>(data = userDMO) {
      var isSelected: Boolean = false
  }
  ```
  
### Dependency Injection
- Use Dagger Hilt for dependency injection.
- In each module, Create `di` package to defined all of object need to inject in project (eg: `Repository`, `API Service`, ...).
- Class in `di` package end with suffix `Module`.
- Example:
  ```kotlin
  @Module
  @InstallIn(SingletonComponent::class)
  object FeatureAModule {
      @Provides
      fun provideUserRepository(userApi: UserApi): UserRepository {
          return UserRepository(userApi)
      }
  }
  ```
  
### Using Local Storage
Use Storage class to save/get data local storage. 
- Setup Key class to defined key using for save/get data in your module.

  ```kotlin
  enum class LoginKey(
    override val key: String,
    override val defValue: String? = null
  ) : IKey {
      LOGIN_RES("LOGIN_RES");
  
      override val prefix: String = "LOGIN"
  }
  ```
- Use Storage class to save/get data local storage.
  ```kotlin
  // Save to local storage
  val loginRes = Storage.get(LoginKey.LOGIN_RES)
  Storage.save(LoginKey.LOGIN_RES, loginRes)
  
  // Get from local storage with default value in Key object
  val loginResCache = Storage.get(LoginKey.LOGIN_RES)
  
  // Get from local storage with other default value
  val loginResCache = Storage.get(LoginKey.LOGIN_RES, "custom default value")
  ```
