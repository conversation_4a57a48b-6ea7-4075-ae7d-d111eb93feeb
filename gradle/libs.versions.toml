[versions]
accompanist = "0.28.0"
accompanist-permissions = "0.37.0"
androidDesugarJdkLibs = "2.0.4"
androidGradlePlugin = "8.4.1"
androidxActivity = "1.8.2"
androidxAppCompat = "1.6.1"
androidxBrowser = "1.8.0"
androidxComposeBom = "2024.04.00"
androidxComposeRuntimeTracing = "1.0.0-beta01"
androidxCore = "1.12.0"
androidxCoreSplashscreen = "1.0.1"
androidxDataStore = "1.0.0"
androidxEspresso = "3.5.1"
androidxHiltNavigationCompose = "1.2.0"
androidxLifecycle = "2.7.0"
androidxMacroBenchmark = "1.2.3"
androidxMetrics = "1.0.0-beta01"
androidxNavigation = "2.8.4"
androidxProfileinstaller = "1.3.1"
androidxStartup = "1.1.1"
androidxTestCore = "1.5.0"
androidxTestExt = "1.1.5"
androidxTestRules = "1.5.0"
androidxTestRunner = "1.5.2"
androidxTracing = "1.2.0"
androidxUiAutomator = "2.3.0"
androidxWindowManager = "1.2.0"
androidxWork = "2.9.0"
coil = "2.4.0"
coreCommon = "1.0.0"
coreLog = "1.0.0"
coreNetwork = "1.0.0"
coreAuthen = "1.0.0"
coreDatabase = "1.0.0"
coreSdkBase = "1.0.0"
coreIbankNetwork = "1.0.0"
coreSecure = "1.0.0"
firebaseBom = "32.8.1"
firebaseCrashlyticsPlugin = "2.9.9"
firebasePerfPlugin = "1.4.2"
github-fatAar = "1.3.8"
gmsPlugin = "4.4.1"
googleOss = "17.0.1"
googleOssPlugin = "0.10.6"
hilt = "2.49"
hiltExt = "1.2.0"
jacoco = "0.8.7"
javapoet = "1.13.0"
junit4 = "4.13.2"
kotlin = "2.0.21"
kotlinxCoroutines = "1.7.3"
kotlinxDatetime = "0.4.0"
kotlinxSerializationJson = "1.5.1"
ksp = "2.0.21-1.0.28"
lint = "31.3.2"
mockitoCore = "3.12.4"
mockitoKotlin = "3.2.0"
mockk = "1.13.5"
okhttp = "4.11.0"
openapiGeneratorGradlePlugin = "7.7.0"
protobuf = "3.24.0"
protobufPlugin = "0.9.4"
retrofit = "2.10.0"
retrofitKotlinxSerializationJson = "1.0.0"
robolectric = "4.10.3"
roborazzi = "1.5.0-alpha-2"
room = "2.6.1"
secrets = "2.0.1"
snakeyaml = "2.2"
turbine = "0.12.1"
materialVersion = "1.11.0"
junit = "1.1.5"
gson = "2.10"
constraintlayout = "2.1.4"
runner = "1.0.2"
espresso-core = "3.0.2"
compose-material3 = "1.3.1"
timber = "5.0.1"
fataar = "1.3.8"
appcompat-v7 = "28.0.0"
localPlugin = "1.0.0"
security-crypto-ktx = "1.1.0-alpha06"
javax-inject = "1"
lottie = "6.2.0"
openApi = "7.7.0"
swagger-annotations = "2.2.19"
moduleGraph = "2.5.0"
mustache = "0.9.10"
coil-compose = "3.0.3"
zxing-core = "3.5.3"
google-auth = "1.4.0"
camera-camera2 = "1.4.1"
camera-lifecycle = "1.4.1"
camera-view = "1.4.1"
barcode-scanning = "17.3.0"
constraintlayout-compose ="1.0.1"
biometric = "1.2.0-alpha05"
bcprov-jdk18on = "1.80"
play-services-auth = "21.3.0"
play-services-auth-api-phone = "18.1.0"
localbroadcastmanager = "1.1.0"
jsoup="1.18.3"
richeditor-compose = "1.0.0-rc10"
exifinterface = "1.4.0"

[libraries]
accompanist-permissions = { group = "com.google.accompanist", name = "accompanist-permissions", version.ref = "accompanist-permissions" }
accompanist-testharness = { group = "com.google.accompanist", name = "accompanist-testharness", version.ref = "accompanist" }
accompanist-pager = { group = "com.google.accompanist", name = "accompanist-pager", version.ref = "accompanist" }
accompanist-pager-indicators = { group = "com.google.accompanist", name = "accompanist-pager-indicators", version.ref = "accompanist" }
accompanist-flowlayout = { group = "com.google.accompanist", name = "accompanist-flowlayout", version.ref = "accompanist" }
android-desugarJdkLibs = { group = "com.android.tools", name = "desugar_jdk_libs", version.ref = "androidDesugarJdkLibs" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "androidxActivity" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "androidxAppCompat" }
androidx-benchmark-macro = { group = "androidx.benchmark", name = "benchmark-macro-junit4", version.ref = "androidxMacroBenchmark" }
androidx-browser = { group = "androidx.browser", name = "browser", version.ref = "androidxBrowser" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "androidxComposeBom" }
androidx-compose-foundation = { group = "androidx.compose.foundation", name = "foundation" }
androidx-compose-foundation-layout = { group = "androidx.compose.foundation", name = "foundation-layout" }
androidx-compose-material-iconsExtended = { group = "androidx.compose.material", name = "material-icons-extended" }
androidx-compose-material3 = { group = "androidx.compose.material3", name = "material3", version.ref = "compose-material3"}
androidx-compose-material3-windowSizeClass = { group = "androidx.compose.material3", name = "material3-window-size-class", version.ref = "compose-material3" }
androidx-compose-runtime = { group = "androidx.compose.runtime", name = "runtime" }
androidx-compose-runtime-livedata = { group = "androidx.compose.runtime", name = "runtime-livedata" }
androidx-compose-runtime-tracing = { group = "androidx.compose.runtime", name = "runtime-tracing", version.ref = "androidxComposeRuntimeTracing" }
androidx-compose-ui-ui = { group = "androidx.compose.ui", name = "ui"}
androidx-compose-ui-test = { group = "androidx.compose.ui", name = "ui-test-junit4" }
androidx-compose-ui-testManifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
androidx-compose-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-compose-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview"}
androidx-compose-ui-util = { group = "androidx.compose.ui", name = "ui-util"}
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "androidxCore" }
androidx-core-splashscreen = { group = "androidx.core", name = "core-splashscreen", version.ref = "androidxCoreSplashscreen" }
androidx-dataStore-core = { group = "androidx.datastore", name = "datastore", version.ref = "androidxDataStore" }
androidx-hilt-navigation-compose = { group = "androidx.hilt", name = "hilt-navigation-compose", version.ref = "androidxHiltNavigationCompose" }
androidx-lifecycle-livedata-ktx = { group = "androidx.lifecycle", name = "lifecycle-livedata-ktx", version.ref = "androidxLifecycle" }
androidx-lifecycle-runtimeCompose = { group = "androidx.lifecycle", name = "lifecycle-runtime-compose", version.ref = "androidxLifecycle" }
androidx-lifecycle-viewModelCompose = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-compose", version.ref = "androidxLifecycle" }
androidx-metrics = { group = "androidx.metrics", name = "metrics-performance", version.ref = "androidxMetrics" }
androidx-navigation-compose = { group = "androidx.navigation", name = "navigation-compose", version.ref = "androidxNavigation" }
androidx-navigation-testing = { group = "androidx.navigation", name = "navigation-testing", version.ref = "androidxNavigation" }
androidx-profileinstaller = { group = "androidx.profileinstaller", name = "profileinstaller", version.ref = "androidxProfileinstaller" }
androidx-test-core = { group = "androidx.test", name = "core", version.ref = "androidxTestCore" }
androidx-test-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "androidxEspresso" }
androidx-test-ext = { group = "androidx.test.ext", name = "junit-ktx", version.ref = "androidxTestExt" }
androidx-test-rules = { group = "androidx.test", name = "rules", version.ref = "androidxTestRules" }
androidx-test-runner = { group = "androidx.test", name = "runner", version.ref = "androidxTestRunner" }
androidx-test-uiautomator = { group = "androidx.test.uiautomator", name = "uiautomator", version.ref = "androidxUiAutomator" }
androidx-tracing-ktx = { group = "androidx.tracing", name = "tracing-ktx", version.ref = "androidxTracing" }
androidx-window-manager = { module = "androidx.window:window", version.ref = "androidxWindowManager" }
androidx-work-ktx = { group = "androidx.work", name = "work-runtime-ktx", version.ref = "androidxWork" }
androidx-work-testing = { group = "androidx.work", name = "work-testing", version.ref = "androidxWork" }
coil-kt = { group = "io.coil-kt", name = "coil", version.ref = "coil" }
coil-kt-compose = { group = "io.coil-kt", name = "coil-compose", version.ref = "coil" }
coil-kt-svg = { group = "io.coil-kt", name = "coil-svg", version.ref = "coil" }
core-common = { module = "vn.com.bidv.ibank2:core-common", version.ref = "coreCommon" }
core-log = { module = "vn.com.bidv.ibank2:core-log", version.ref = "coreLog" }
core-network = { module = "vn.com.bidv.ibank2:core-network", version.ref = "coreNetwork" }
core-authen = { module = "vn.com.bidv.ibank2:core-authen", version.ref = "coreAuthen" }
core-database = { module = "vn.com.bidv.ibank2:core-database", version.ref = "coreDatabase" }
core-sdkbase = { module = "vn.com.bidv.ibank2:core-sdkbase", version.ref = "coreSdkBase" }
core-ibank-network = { module = "vn.com.bidv.ibank2:core-ibank-network", version.ref = "coreIbankNetwork" }
core-secure = { module = "vn.com.bidv.ibank2:core-secure", version.ref = "coreSecure" }
fat-aar = { module = "com.github.kezong:fat-aar", version.ref = "github-fatAar" }
firebase-analytics = { group = "com.google.firebase", name = "firebase-analytics-ktx" }
firebase-bom = { group = "com.google.firebase", name = "firebase-bom", version.ref = "firebaseBom" }
firebase-cloud-messaging = { group = "com.google.firebase", name = "firebase-messaging-ktx" }
firebase-crashlytics = { group = "com.google.firebase", name = "firebase-crashlytics-ktx" }
firebase-performance = { group = "com.google.firebase", name = "firebase-perf-ktx" }
firebase-performance-gradle = { group = "com.google.firebase", name = "perf-plugin", version.ref = "firebasePerfPlugin" }
google-oss-licenses = { group = "com.google.android.gms", name = "play-services-oss-licenses", version.ref = "googleOss" }
google-oss-licenses-plugin = { group = "com.google.android.gms", name = "oss-licenses-plugin", version.ref = "googleOssPlugin" }
hilt-android = { group = "com.google.dagger", name = "hilt-android", version.ref = "hilt" }
hilt-android-testing = { group = "com.google.dagger", name = "hilt-android-testing", version.ref = "hilt" }
hilt-compiler = { group = "com.google.dagger", name = "hilt-android-compiler", version.ref = "hilt" }
hilt-ext-compiler = { group = "androidx.hilt", name = "hilt-compiler", version.ref = "hiltExt" }
hilt-ext-work = { group = "androidx.hilt", name = "hilt-work", version.ref = "hiltExt" }
javapoet = { module = "com.squareup:javapoet", version.ref = "javapoet" }
junit4 = { group = "junit", name = "junit", version.ref = "junit4" }
kotlin-stdlib = { group = "org.jetbrains.kotlin", name = "kotlin-stdlib-jdk8", version.ref = "kotlin" }
kotlinx-coroutines-android = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-android", version.ref = "kotlinxCoroutines" }
kotlinx-coroutines-guava = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-guava", version.ref = "kotlinxCoroutines" }
kotlinx-coroutines-test = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-test", version.ref = "kotlinxCoroutines" }
kotlinx-datetime = { group = "org.jetbrains.kotlinx", name = "kotlinx-datetime", version.ref = "kotlinxDatetime" }
kotlinx-serialization-json = { group = "org.jetbrains.kotlinx", name = "kotlinx-serialization-json", version.ref = "kotlinxSerializationJson" }
lint-api = { group = "com.android.tools.lint", name = "lint-api", version.ref = "lint" }
mockito-core = { module = "org.mockito:mockito-core", version.ref = "mockitoCore" }
mockito-kotlin = { module = "org.mockito.kotlin:mockito-kotlin", version.ref = "mockitoKotlin" }
mockk = { module = "io.mockk:mockk", version.ref = "mockk" }
okhttp-logging = { group = "com.squareup.okhttp3", name = "logging-interceptor", version.ref = "okhttp" }
okhttp = { group = "com.squareup.okhttp3", name = "okhttp", version.ref = "okhttp" }
openapi-generator-gradle-plugin = { module = "org.openapitools:openapi-generator-gradle-plugin", version.ref = "openapiGeneratorGradlePlugin" }
protobuf-kotlin-lite = { group = "com.google.protobuf", name = "protobuf-kotlin-lite", version.ref = "protobuf" }
protobuf-protoc = { group = "com.google.protobuf", name = "protoc", version.ref = "protobuf" }
retrofit-core = { group = "com.squareup.retrofit2", name = "retrofit", version.ref = "retrofit" }
retrofit-kotlin-serialization = { group = "com.jakewharton.retrofit", name = "retrofit2-kotlinx-serialization-converter", version.ref = "retrofitKotlinxSerializationJson" }
retrofit-gson = { group = "com.squareup.retrofit2", name = "converter-gson", version.ref = "retrofit" }
robolectric = { group = "org.robolectric", name = "robolectric", version.ref = "robolectric" }
robolectric-shadows = { group = "org.robolectric", name = "shadows-framework", version.ref = "robolectric" }
roborazzi = { group = "io.github.takahirom.roborazzi", name = "roborazzi", version.ref = "roborazzi" }
room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "room" }
room-ktx = { group = "androidx.room", name = "room-ktx", version.ref = "room" }
room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "room" }
snakeyaml = { module = "org.yaml:snakeyaml", version.ref = "snakeyaml" }
turbine = { group = "app.cash.turbine", name = "turbine", version.ref = "turbine" }
google-material = { module = "com.google.android.material:material", version.ref = "materialVersion" }
gson = { module = "com.google.code.gson:gson", version.ref = "gson" }
work-testing = { group = "androidx.work", name = "work-testing", version = "2.9.0" }
junit = { group = "androidx.test.ext", name = "junit", version.ref = "junit" }
constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
runner = { group = "com.android.support.test", name = "runner", version.ref = "runner" }
espresso-core = { group = "com.android.support.test.espresso", name = "espresso-core", version.ref = "espresso-core" }
timber = {group = "com.jakewharton.timber", name = "timber", version.ref="timber"}
security-crypto-ktx = {group = "androidx.security", name = "security-crypto-ktx", version.ref="security-crypto-ktx"}
javax-inject = {group = "javax.inject", name = "javax.inject", version.ref="javax-inject"}
lottie = {group = "com.airbnb.android", name = "lottie-compose", version.ref="lottie"}
swagger-annotations = {group = "io.swagger.core.v3", name = "swagger-annotations", version.ref="swagger-annotations"}
mustache-java = { group = "com.github.spullara.mustache.java", name = "compiler", version.ref = "mustache" }
coil-compose = { group = "io.coil-kt.coil3", name = "coil-compose", version.ref = "coil-compose" }
coil-network = { group = "io.coil-kt.coil3", name = "coil-network-okhttp", version.ref = "coil-compose" }
zxing-core = { group = "com.google.zxing", name = "core", version.ref = "zxing-core" }
google-auth = { group = "com.warrenstrange", name = "googleauth", version.ref = "google-auth" }
camera-camera2 = { group = "androidx.camera", name = "camera-camera2", version.ref = "camera-camera2" }
camera-lifecycle = { group = "androidx.camera", name = "camera-lifecycle", version.ref = "camera-lifecycle" }
camera-view = { group = "androidx.camera", name = "camera-view", version.ref = "camera-view" }
barcode-scanning = { group = "com.google.mlkit", name = "barcode-scanning", version.ref = "barcode-scanning" }
constraintlayout-compose = { group = "androidx.constraintlayout", name = "constraintlayout-compose", version.ref = "constraintlayout-compose" }
biometric = { group = "androidx.biometric", name = "biometric", version.ref = "biometric" }
bcprov-jdk18on = { group = "org.bouncycastle", name = "bcprov-jdk18on", version.ref = "bcprov-jdk18on" }
play-services-auth = { group = "com.google.android.gms", name = "play-services-auth", version.ref = "play-services-auth" }
play-services-auth-api-phone = { group = "com.google.android.gms", name = "play-services-auth-api-phone", version.ref = "play-services-auth-api-phone" }
localbroadcastmanager = { group = "androidx.localbroadcastmanager", name = "localbroadcastmanager", version.ref = "localbroadcastmanager" }
jsoup = { group = "org.jsoup", name = "jsoup", version.ref = "jsoup" }
richeditor-compose = { group = "com.mohamedrejeb.richeditor", name = "richeditor-compose", version.ref = "richeditor-compose" }

# Dependencies of the included build-logic
android-gradlePlugin = { group = "com.android.tools.build", name = "gradle", version.ref = "androidGradlePlugin" }
firebase-crashlytics-gradlePlugin = { group = "com.google.firebase", name = "firebase-crashlytics-gradle", version.ref = "firebaseCrashlyticsPlugin" }
firebase-performance-gradlePlugin = { group = "com.google.firebase", name = "perf-plugin", version.ref = "firebasePerfPlugin" }
kotlin-gradlePlugin = { group = "org.jetbrains.kotlin", name = "kotlin-gradle-plugin", version.ref = "kotlin" }
ksp-gradlePlugin = { group = "com.google.devtools.ksp", name = "com.google.devtools.ksp.gradle.plugin", version.ref = "ksp" }
appcompat-v7 = { group = "com.android.support", name = "appcompat-v7", version.ref = "appcompat-v7" }
androidx-exifinterface = { group = "androidx.exifinterface", name = "exifinterface", version.ref = "exifinterface" }

[plugins]
android-application = { id = "com.android.application", version.ref = "androidGradlePlugin" }
android-library = { id = "com.android.library", version.ref = "androidGradlePlugin" }
android-test = { id = "com.android.test", version.ref = "androidGradlePlugin" }
firebase-crashlytics = { id = "com.google.firebase.crashlytics", version.ref = "firebaseCrashlyticsPlugin" }
firebase-perf = { id = "com.google.firebase.firebase-perf", version.ref = "firebasePerfPlugin" }
gms = { id = "com.google.gms.google-services", version.ref = "gmsPlugin" }
hilt = { id = "com.google.dagger.hilt.android", version.ref = "hilt" }
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
kotlin-compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
protobuf = { id = "com.google.protobuf", version.ref = "protobufPlugin" }
roborazzi = { id = "io.github.takahirom.roborazzi", version.ref = "roborazzi" }
secrets = { id = "com.google.android.libraries.mapsplatform.secrets-gradle-plugin", version.ref = "secrets" }
org-jetbrains-kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
fataar = {id = "com.kezong.fat-aar", version.ref = "fataar"}
openapi-generator = {id = "org.openapi.generator", version.ref = "openApi"}
module-graph = { id = "com.jraska.module.graph.assertion", version.ref = "moduleGraph" }

local-jvm-library = { id = "sdk.jvm.library", version.ref = "localPlugin"}
local-android-library = { id = "sdk.android.library", version.ref = "localPlugin"}
local-android-application = { id = "sdk.android.application", version.ref = "localPlugin"}
local-android-application-compose = { id = "sdk.android.application.compose", version.ref = "localPlugin"}
local-android-application-flavor = { id = "sdk.android.application.flavors", version.ref = "localPlugin"}
local-android-application-firebase = { id = "sdk.android.application.firebase", version.ref = "localPlugin"}
local-android-compose = { id = "sdk.android.compose", version.ref = "localPlugin"}
local-android-lint = { id = "sdk.android.lint", version.ref = "localPlugin"}
local-app-favorite = { id = "sdk.android.application.flavors", version.ref = "localPlugin"}
local-app-gendimens = { id = "sdk.android.gendimens", version.ref = "localPlugin"}
local-android-hilt = { id = "sdk.android.hilt", version.ref = "localPlugin"}
local-networkConfig = { id = "sdk.network.config", version.ref = "localPlugin"}
local-generate-figma = { id = "sdk.generate.figma", version.ref = "localPlugin" }
local-report-jacoco = { id = "sdk.report.jacoco", version.ref = "localPlugin"}
local-ibank-feature = { id = "ibank.feature", version.ref = "localPlugin"}
local-android-openapi-generate = { id = "sdk.openapi.generate", version.ref = "localPlugin"}
local-maven-publish = { id = "sdk.android.maven-publish", version.ref = "localPlugin"}
local-android-room = { id = "sdk.android.room", version.ref = "localPlugin"}
local-publish-local-repo = { id = "sdk.publish.localRepo", version.ref = "localPlugin"}
local-ibank-protect = { id = "ibank.protect", version.ref = "localPlugin"}

[bundles]
retrofit = ["retrofit-core", "retrofit-kotlin-serialization"]
