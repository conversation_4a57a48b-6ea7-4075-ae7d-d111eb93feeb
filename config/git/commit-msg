#!/bin/sh

# Get the top-level directory of the superproject if in a submodule, otherwise the current project
TOP_LEVEL_DIR=$(git rev-parse --show-superproject-working-tree 2>/dev/null || git rev-parse --show-toplevel)

TEMPLATE="$TOP_LEVEL_DIR/config/git/commit-template.txt"
MESSAGE=$(cat "$1")

if ! echo "$MESSAGE" | grep -qE "^(fix:|feat:|refactor:|perf:|docs:|ci:|build:|revert:|chore:|vendor:|security:|other:)"; then
  echo "Please use the following template:"
  cat "$TEMPLATE"
  echo ""
  echo "Error: Commit message does not follow the template."
  exit 1
fi