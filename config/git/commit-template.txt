Mỗi commit message bao gồm header, body, footer theo cấu trúc như sau:
<type>[optional scope]: [Optional-ID Jira] <Description>
<BLANK LINE>
[optional body]
<BLANK LINE>
[optional footer]

Trong đó:

<type>: <PERSON>ân loại commit. Type bao gồm các loại sau:
    - fix: sửa lỗi trong hệ thống, sửa lỗi trong codebase
    - feat: code thêm 1 tính năng mới
    - refactor: thay đổi code mà không phải sửa lỗi (fix bug), cũng không thêm tính năng
      mới.
    - perf: thay đổi code giúp code chạy tốc độ cao hơn, cải tiến về mặt hiệu năng xử lý.
    - docs: thêm/thay đổi trong các file văn bản. VD: trong các file .md
    - ci: thay đổi các file cấu hình CI;
    - build: thay đổi liên quan đến hệ thống hoặc các thư viện bên ngoài ảnh hưởng đến
      tiến trình build;
    - revert: quay lại các commit cũ;
    - chore: các sửa đổi không liên quan tới code: sửa lỗi đánh máy, cập nhật , thay đổi
      như khoảng trắng (white-space), định dạng (format), ...không làm thay đổi ý nghĩa
      của code
    - vendor: cập nhật version cho các dependencies, packages.
    - security: sửa lỗi về bảo mật, như lỗi sau khi quét sonarqube, các lỗi từ Bộ phận an
      ninh bảo mật;
    - other: Không thuộc các loại trên

[optional scope]: Không bắt buộc, để chỉ phạm vi ảnh hưởng của commit. Để trống khi
commit không ảnh hưởng tới chức năng khác.


<Description>: Mô tả ngắn gọn nội dung sửa đổi trong commit

[optional body]:
    - Không bắt buộc
    - Mô tả dài và chi tiết hơn những gì đã sửa đổi. Bắt buộc phải cách phần <Optional-
      Description> 1 dòng trắng

[optional footer]:
    - Không bắt buộc
    - Mô tả dài và chi tiết hơn những gì đã sửa đổi. Bắt buộc phải cách phần (optional
      body)1 dòng trắng
    - BREAKING CHANGE: thể hiện những thay đổi gây ảnh hưởng lớn đến mã nguồn
      (VD: thay đổi kiểu dữ liệu, cách lấy dữ liệu,..) mục đích để cảnh báo mọi người để
      tránh phát sinh các vấn đề.

VD về các commit theo cú pháp:
- Đối với build-logic, build-protect: "feat: [module1, module2,...] thêm chức năng vân tay". (module1, module2: các module của core-private ảnh hưởng)
- Đối với core-private, core-public: "feat: [module1, module2,...] thêm chức năng vân tay". (module1, module2: các module của core-private, core-public có thay đổi code)
- Đối với feature: "feat: thêm chức năng vân tay"
* Note: cần ghi đúng thứ tự depend của các module