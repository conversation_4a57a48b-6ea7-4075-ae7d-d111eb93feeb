<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="165dp"
    android:height="108dp"
    android:viewportWidth="165"
    android:viewportHeight="108">
  <path
      android:pathData="M208.57,10.04C206.06,5.77 202.5,2.52 198.85,-0.75C187.13,-11.16 172.43,-18.54 157.42,-21.9C156.3,-22.14 155.58,-23.16 155.69,-24.26C157.28,-38.5 155.55,-53.06 150.34,-66.66C148.56,-65.86 146.31,-64.68 143.61,-62.97L143.41,-62.84C148.67,-48.41 149.76,-32.96 146.84,-18.29C146.59,-17 147.48,-15.76 148.81,-15.56C169.17,-12.7 188.46,-2.35 201.94,13.43C190.22,23.36 176.27,30.74 161.32,34.38C160.26,34.63 159.53,35.59 159.59,36.69C160.51,54.28 156.41,71.96 148.03,87.46C199.04,105.49 237.55,54.23 208.6,10.07L208.57,10.04Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="181.19"
          android:startY="-65.97"
          android:endX="180.59"
          android:endY="88.54"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M148.52,-79C130.78,-71.37 115.16,-57.77 105.47,-41.53C104.89,-40.57 103.69,-40.15 102.66,-40.62C89.74,-46.43 75.04,-49.35 60.42,-48.72C60.59,-46.79 60.95,-44.26 61.65,-41.14V-40.98C77.21,-41.47 92.36,-37.73 105.61,-30.43C106.78,-29.8 108.26,-30.24 108.84,-31.45C117.75,-49.71 133.82,-64.6 153.03,-72.5L154.12,-69.89C159.55,-56.51 161.69,-42 160.72,-27.76C160.63,-26.69 161.33,-25.73 162.39,-25.42C176,-21.79 188.64,-15.24 199.56,-6.26H199.59C202.34,-3.92 205.04,-1.5 207.58,1.12C211.56,-3.81 214.54,-10.06 216.21,-15.79C231.8,-57.11 188.7,-96.01 148.52,-79.03V-79Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="71.42"
          android:startY="-78.36"
          android:endX="218.41"
          android:endY="-27.04"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M136.5,72.81L136.64,72.53C135.97,72.09 135.33,71.65 134.69,71.18C122.66,62.51 113.5,51.33 107.38,38.39C106.82,37.21 105.35,36.71 104.18,37.35C86.05,46.85 64.19,49.79 44,44.78C47.6,29.89 54.45,15.87 64.39,4.2C65.08,3.37 65.11,2.16 64.41,1.34C53.25,-12.32 46.06,-28.98 43.64,-46.54V-46.71C38.49,-45.38 33.14,-42.82 28.86,-39.99C-20.29,-8 7.92,57.78 64.11,54.94C77.05,54.94 89.53,52.32 100.94,47.48C102,47.04 103.26,47.42 103.81,48.42C110.8,60.61 120.94,71.51 133.1,79.5C134.11,77.85 135.3,75.64 136.53,72.81H136.5Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="106.88"
          android:startY="96.52"
          android:endX="18.48"
          android:endY="-28.91"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M189.82,7.37L189.68,7.26C177.43,16.68 162.92,22.43 147.91,24.27C146.57,24.44 145.66,25.65 145.91,26.97C149.42,46.91 145.63,68.52 134.41,85.86L132.07,84.38C119.65,76.53 109.49,66.32 101.83,54.17C101.25,53.27 100.1,52.88 99.07,53.27C82.53,59.54 64.16,61.19 46.67,58.06C44.08,107.56 102.08,133.13 136.22,95.47C151.09,79.56 156.46,54.37 154.62,33.17C154.54,32.07 155.35,30.99 156.43,30.74C170.38,27.91 184.03,21.69 195.5,12.63C194.16,11.2 192.3,9.41 189.82,7.37Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="203.91"
          android:startY="42.58"
          android:endX="55.94"
          android:endY="90.19"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M137.15,-88.99C137.15,-88.99 137.15,-88.99 137.13,-89.02C102.46,-126.6 41.84,-96.95 48.25,-47.5C48.25,-47.5 48.25,-47.5 48.25,-47.47C50.56,-29.58 58.66,-11.96 70.55,1.34C71.3,2.2 71.27,3.46 70.55,4.29C61.03,14.67 53.65,27.61 49.78,41.57C51.67,42.01 54.18,42.45 57.3,42.75H57.57C61.89,28.02 70.19,14.89 81.3,4.65C82.27,3.74 82.27,2.22 81.33,1.31C80.05,0.08 78.79,-1.19 77.59,-2.54C65.01,-16.03 56.99,-34.42 55.54,-53.06C70.86,-54.33 86.73,-52.12 100.87,-46.43C101.9,-46.01 103.04,-46.37 103.66,-47.28C113.32,-61.95 127.07,-73.87 143.2,-81.61C141.5,-84.2 139.41,-86.7 137.18,-88.99H137.15Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="25.06"
          android:startY="22.63"
          android:endX="116.8"
          android:endY="-103.5"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
