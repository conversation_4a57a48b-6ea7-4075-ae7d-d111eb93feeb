package vn.com.bidv.designsystem.component.datepicker

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.component.datepicker.model.CalendarMode
import vn.com.bidv.designsystem.component.datepicker.model.DatePickerConfig
import vn.com.bidv.designsystem.component.datepicker.model.PickerType
import vn.com.bidv.designsystem.component.datepicker.utils.DateTimeFormats
import vn.com.bidv.designsystem.component.datepicker.utils.getMonthName
import vn.com.bidv.designsystem.component.datepicker.utils.toFormattedString
import vn.com.bidv.designsystem.component.navigation.button.IBankNormalButton
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonSize
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonType
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import java.time.temporal.ChronoUnit
import java.util.Calendar
import java.util.Date
import vn.com.bidv.designsystem.R as RDesignSystem
import vn.com.bidv.localization.R as RLocalization

@Composable
fun IBankCalendarPicker(
    modifier: Modifier = Modifier,
    start: Date? = null,
    end: Date? = null,
    mode: CalendarMode = CalendarMode.SINGLE,
    negativeButtonText: String = stringResource(RLocalization.string.xoa_bo_loc),
    positiveButtonText: String = stringResource(RLocalization.string.xac_nhan),
    config: DatePickerConfig,
    onPickerTypeStateChange: (PickerType) -> Unit = {},
    onDateSelected: (selected: Date?) -> Unit = {},
    onDateRangeSelected: (from: Date?, to: Date?) -> Unit = { from, to -> },
    onCancel: () -> Unit = {},
) {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current
    var datePickerConfig by remember { mutableStateOf(config) }
    val calendar = Calendar.getInstance()
    var startDate by remember { mutableStateOf(start) }
    var endDate by remember {
        if (mode == CalendarMode.SINGLE) {
            mutableStateOf(start)
        } else {
            mutableStateOf(end)
        }
    }

    if (start != null) {
        calendar.time = start
    }
    var displayedMonth by remember { mutableIntStateOf(calendar.get(Calendar.MONTH)) }
    var displayedYear by remember { mutableIntStateOf(calendar.get(Calendar.YEAR)) }
    var startYearSelect by remember {
        val currentYear = Calendar.getInstance().get(Calendar.YEAR)
        mutableIntStateOf(
            currentYear - (currentYear + 7 - displayedYear) / 15 * 15 - 7
        )
    }
    var pickerState by remember { mutableStateOf(PickerType.DAY) }

    LaunchedEffect(pickerState) {
        onPickerTypeStateChange(pickerState)
    }

    Column(modifier = modifier) {
        if (mode == CalendarMode.RANGE && pickerState == PickerType.DAY) {
            Text(
                text = "${
                    startDate?.toFormattedString(DateTimeFormats.DATE_FORMAT) ?: stringResource(
                        RLocalization.string.ngay_bat_dau
                    )
                } - ${
                    (endDate ?: startDate)?.toFormattedString(DateTimeFormats.DATE_FORMAT) ?: stringResource(
                        RLocalization.string.ngay_ket_thuc
                    )
                }",
                modifier = Modifier.padding(
                    horizontal = IBSpacing.spacingM, vertical = IBSpacing.spacingXs
                ),
                style = LocalTypography.current.titleTitle_l,
                color = LocalColorScheme.current.contentPlaceholder
            )
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = IBSpacing.spacingXs, horizontal = IBSpacing.spacingM),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(modifier = Modifier.clickable {
                pickerState = when (pickerState) {
                    PickerType.DAY -> PickerType.YEAR
                    PickerType.MONTH -> PickerType.DAY
                    PickerType.YEAR -> PickerType.DAY
                }
            }, verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = when (pickerState) {
                        PickerType.DAY -> "${getMonthName(displayedMonth)}, $displayedYear"
                        PickerType.YEAR -> "${getMonthName(displayedMonth)}, $displayedYear"
                        PickerType.MONTH -> "$displayedYear"
                    }, style = typography.titleTitle_s
                )
                Spacer(modifier = Modifier.width(IBSpacing.spacingXs))
                Icon(
                    imageVector =
                    when (pickerState) {
                        PickerType.DAY -> ImageVector.vectorResource(id = RDesignSystem.drawable.arrow_bottom_outline)
                        PickerType.MONTH, PickerType.YEAR -> ImageVector.vectorResource(id = RDesignSystem.drawable.arrow_top_outline)
                    },
                    contentDescription = null,
                    modifier = Modifier.size(20.dp)
                )
            }

            if (pickerState == PickerType.DAY || pickerState == PickerType.YEAR) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    IconButton(
                        modifier = Modifier.size(32.dp),
                        onClick = {
                            if (pickerState == PickerType.DAY) {
                                val newMonthCalendar = Calendar.getInstance()
                                newMonthCalendar.set(displayedYear, displayedMonth, 1)
                                newMonthCalendar.add(Calendar.MONTH, -1)

                                if (datePickerConfig.minDate != null) {
                                    val minDateCalendar = Calendar
                                        .getInstance()
                                        .apply {
                                            time = datePickerConfig.minDate!!
                                        }
                                    if (newMonthCalendar.before(minDateCalendar)) {
                                        // Reset newMonthCalendar to minDate's year and month
                                        newMonthCalendar.set(
                                            minDateCalendar.get(Calendar.YEAR),
                                            minDateCalendar.get(Calendar.MONTH),
                                            1
                                        )
                                    }
                                }

                                displayedMonth = newMonthCalendar.get(Calendar.MONTH)
                                displayedYear = newMonthCalendar.get(Calendar.YEAR)
                            } else {
                                startYearSelect -= 15
                            }
                        }
                    ) {
                        val icon =
                            if (pickerState == PickerType.DAY) RDesignSystem.drawable.arrow_left_outline else RDesignSystem.drawable.arrow_top_outline
                        Icon(
                            modifier = Modifier
                                .size(20.dp),
                            imageVector = ImageVector.vectorResource(id = icon),
                            contentDescription = null,
                        )
                    }

                    Spacer(modifier = Modifier.width(IBSpacing.spacingXs))

                    IconButton(
                        modifier = Modifier.size(32.dp),
                        onClick = {
                            if (pickerState == PickerType.DAY) {
                                val newMonthCalendar = Calendar.getInstance()
                                newMonthCalendar.set(displayedYear, displayedMonth, 1)
                                newMonthCalendar.add(Calendar.MONTH, 1)
                                if (datePickerConfig.maxDate != null) {
                                    val maxDateCalendar = Calendar
                                        .getInstance()
                                        .apply {
                                            time = datePickerConfig.maxDate!!
                                        }
                                    if (newMonthCalendar.after(maxDateCalendar)) {
                                        // Reset newMonthCalendar to maxDate's year and month
                                        newMonthCalendar.set(
                                            maxDateCalendar.get(Calendar.YEAR),
                                            maxDateCalendar.get(Calendar.MONTH),
                                            1
                                        )
                                    }
                                }

                                displayedMonth = newMonthCalendar.get(Calendar.MONTH)
                                displayedYear = newMonthCalendar.get(Calendar.YEAR)
                            } else if (pickerState == PickerType.YEAR) {
                                startYearSelect += 15
                            }
                        }
                    ) {
                        val icon =
                            if (pickerState == PickerType.DAY) RDesignSystem.drawable.arrow_right_outline else RDesignSystem.drawable.arrow_bottom_outline
                        Icon(
                            modifier = Modifier.size(20.dp),
                            imageVector = ImageVector.vectorResource(id = icon),
                            contentDescription = null,
                        )
                    }
                }
            }
        }

        when (pickerState) {
            PickerType.DAY -> {
                DayPicker(
                    modifier = Modifier.fillMaxWidth(),
                    startDate = startDate,
                    endDate = endDate,
                    displayedMonth = displayedMonth,
                    displayedYear = displayedYear,
                    firstDayOfWeek = config.firstDayOfWeek,
                    datePickerConfig = datePickerConfig,
                ) { dateSelected ->
                    when (mode) {
                        CalendarMode.SINGLE -> {
                            startDate = dateSelected
                            endDate = dateSelected
                        }

                        CalendarMode.RANGE -> {
                            if (startDate == null || endDate != null) {
                                startDate = dateSelected
                                endDate = null
                            } else {
                                if (dateSelected.before(startDate)) {
                                    endDate = startDate
                                    startDate = dateSelected
                                } else {
                                    endDate = dateSelected
                                }
                            }
                            // Thay doi minDate/maxDate khi user chon startDate
                            if (datePickerConfig.maxDateRange != null && (endDate == null)) {
                                val maxRange = datePickerConfig.maxDateRange!!
                                val newMinDate = startDate?.addDays(-maxRange)
                                    ?.let { it.coerceAtLeast(config.minDate ?: it) }
                                val newMaxDate = startDate?.addDays(maxRange)
                                    ?.let { it.coerceAtMost(config.maxDate ?: it) }
                                datePickerConfig = config.copy(
                                    minDate = newMinDate,
                                    maxDate = newMaxDate,
                                )
                            } else {
                                datePickerConfig = config
                            }
                        }
                    }
                }
            }

            PickerType.MONTH -> {
                val isContainedSelectedMonth = displayedYear - 1900 == startDate?.year
                MonthPicker(
                    modifier = Modifier.fillMaxWidth(),
                    monthSelected = displayedMonth,
                    datePickerConfig = datePickerConfig,
                    year = displayedYear,
                    isContainedSelectedMonth = isContainedSelectedMonth,
                ) { monthSelected ->
                    displayedMonth = monthSelected
                    pickerState = PickerType.DAY
                }
            }

            PickerType.YEAR -> {
                YearPicker(
                    modifier = Modifier.fillMaxWidth(),
                    startYear = startYearSelect,
                    selected = displayedYear,
                    datePickerConfig = datePickerConfig,
                ) { yearSelected ->
                    pickerState = PickerType.MONTH
                    displayedYear = yearSelected
                }
            }
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {

            IBankNormalButton(
                text = if (pickerState == PickerType.DAY) negativeButtonText else stringResource(
                    RLocalization.string.huy
                ),
                size = NormalButtonSize.L(typography),
                type = NormalButtonType.SECONDARYGRAY(colorScheme),
                onClick = {
                    when (pickerState) {
                        PickerType.DAY -> {
                            startDate = null
                            endDate = null
                            // Thay doi minDate/maxDate khi user chon startDate
                            datePickerConfig = config
                            onCancel()
                        }

                        PickerType.MONTH -> {
                            pickerState = PickerType.YEAR
                        }

                        PickerType.YEAR -> {
                            pickerState = PickerType.DAY
                        }
                    }
                },
                modifier = Modifier.weight(1f),
            )

            Spacer(modifier = Modifier.width(16.dp))

            IBankNormalButton(
                text = if(pickerState == PickerType.DAY) positiveButtonText else stringResource(RLocalization.string.xac_nhan),
                size = NormalButtonSize.L(typography),
                type = NormalButtonType.PRIMARY(colorScheme),
                onClick = {
                    when (pickerState) {
                        PickerType.DAY -> {
                            when (mode) {
                                CalendarMode.SINGLE -> onDateSelected(startDate)
                                CalendarMode.RANGE -> onDateRangeSelected(
                                    startDate,
                                    endDate ?: startDate
                                )
                            }
                        }

                        PickerType.MONTH -> pickerState = PickerType.DAY

                        PickerType.YEAR -> pickerState = PickerType.MONTH
                    }
                },
                modifier = Modifier.weight(1f)
            )
        }
    }
}

private fun Date.addDays(days: Long): Date {
    return Date.from(toInstant().plus(days, ChronoUnit.DAYS))
}

@Preview(showBackground = true)
@Composable
fun JetnewsApp() {
    IBankCalendarPicker(modifier = Modifier, config = DatePickerConfig.build { }, onDateSelected = {

    }, onDateRangeSelected = { from, to ->

    })
}
