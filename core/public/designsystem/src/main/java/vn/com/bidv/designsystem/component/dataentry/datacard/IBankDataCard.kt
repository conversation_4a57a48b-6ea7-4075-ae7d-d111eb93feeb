package vn.com.bidv.designsystem.component.dataentry.datacard

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.dataentry.IBankCheckBox
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.log.BLogUtil

/**
 * A composable function for displaying a data card with a header, optional content, and footer.
 * It supports an optional checkbox to track selection state.
 *
 * @param modifier applied to the root container of the card.
 * @param showCheckbox A boolean flag indicating whether a checkbox should be displayed on the card.
 * @param isChecked A boolean value representing the checked state of the checkbox.
 * @param cardHeader A composable lambda representing the card's header section.
 * @param cardContent An optional composable lambda representing the card's content section.
 * @param cardFooter A composable lambda representing the card's footer section.
 * @param onCheckedChange A lambda triggered when the checkbox state changes.
 **/
@Composable
fun IBankDataCard(
    modifier: Modifier = Modifier,
    showCheckbox: Boolean = false,
    isChecked: Boolean,
    cardHeader: @Composable () -> Unit,
    cardContent: (@Composable () -> Unit)? = null,
    cardFooter: @Composable () -> Unit,
    onCheckedChange: (Boolean) -> Unit,
) {
    val colorScheme = LocalColorScheme.current
    Row(
        modifier = modifier
            .fillMaxWidth()
    ) {
        if (showCheckbox) {
            IBankCheckBox(
                checked = isChecked,
                modifier = Modifier.padding(end = IBSpacing.spacingXs),
                onCheckedChange = {
                    if (isChecked != it.checked) {
                        onCheckedChange(it.checked)
                    }
                },
            )
        }

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clickable {
                    onCheckedChange(!isChecked)
                }
                .clip(shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL))
                .border(
                    width = if (isChecked) IBBorderDivider.borderDividerS else 0.dp,
                    color = if (isChecked) colorScheme.borderBrandSecondary else Color.Transparent,
                    shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL)
                )
                .background(color = colorScheme.bgMainTertiary)
        ) {
            cardHeader()

            cardContent?.let {
                Spacer(modifier = Modifier.height(8.dp))
                it()
            }

            Spacer(modifier = Modifier.height(8.dp))
            cardFooter()
        }
    }
}

@Composable
@Preview(showBackground = true)
fun DataCardPreview() {
    var isChecked by remember { mutableStateOf(false) }
    IBankDataCard(
        showCheckbox = true,
        isChecked = isChecked,
        onCheckedChange = {
            if (it != isChecked) {
                isChecked = it
            }
        },
        cardHeader = {
            IBankCardHeader(
                title = "Title",
                subtitle = "Supporting text",
                showDivider = true,
                icon = ImageVector.vectorResource(id = R.drawable.information_circle),
            )
        },
        cardContent = {
            Surface(
                modifier = Modifier.padding(
                    vertical = IBSpacing.spacingXs,
                    horizontal = IBSpacing.spacingM
                )
            ) { Text("This is your dynamic custom content") }
        },
        cardFooter = {
            IBankCardFooter(
                footerType = FooterType.ActionButton(
                    buttonLeft = ButtonAction(
                        "Button Left",
                        icon = ImageVector.vectorResource(id = R.drawable.send),
                        onClick = {
                            BLogUtil.d("onButtonLeftClick")
                        }
                    ),
                    buttonRight = ButtonAction(
                        "Button Left",
                        icon = ImageVector.vectorResource(id = R.drawable.transfer),
                        onClick = {
                            BLogUtil.d("onButtonLeftClick")
                        }
                    )
                )
            )
        },
    )
}

