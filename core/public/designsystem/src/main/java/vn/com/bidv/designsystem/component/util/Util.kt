package vn.com.bidv.designsystem.component.util

import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.offset
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import vn.com.bidv.common.utils.Utils
import vn.com.bidv.designsystem.theme.IBSpacing

/**
 * Adds a dashed border to the Composable element.
 *
 * @param strokeWidth The width of the border stroke in Dp.
 * @param color The color of the dashed border.
 * @param cornerRadiusDp The corner radius of the border in Dp.
 * @return A modified [Modifier] with a dashed border applied.
 */

fun Modifier.dashedBorder(strokeWidth: Dp, color: Color, cornerRadiusDp: Dp) = composed(
    factory = {
        val density = LocalDensity.current
        val strokeWidthPx = density.run { strokeWidth.toPx() }
        val cornerRadiusPx = density.run { cornerRadiusDp.toPx() }

        this.then(
            Modifier.drawWithCache {
                onDrawBehind {
                    val stroke = Stroke(
                        width = strokeWidthPx,
                        pathEffect = PathEffect.dashPathEffect(floatArrayOf(10f, 10f), 0f)
                    )

                    drawRoundRect(
                        color = color,
                        style = stroke,
                        cornerRadius = CornerRadius(cornerRadiusPx)
                    )
                }
            }
        )
    }
)

/**
 * A Modifier extension that adjusts the bottom padding of a Composable
 * based on the position of the keyboard to prevent UI elements from being overlapped.
 *
 * This function dynamically calculates the space between the bottom of the Composable
 * and the root layout, adjusting the padding accordingly to ensure proper visibility.
 *
 * @param paddingValue Additional spacing (in dp) to increase the gap between the keyboard and the Composable.
 *                     This helps create extra breathing room for better usability.
 * @return A Modifier with position-aware IME (keyboard) padding.
 */
fun Modifier.setImePadding(paddingValue: Dp = IBSpacing.spacingS) = composed {
    val imeInsets = WindowInsets.ime
    val keyboardHeight = imeInsets.getBottom(LocalDensity.current)
    val isKeyboardVisible = keyboardHeight > 0

    this.then(
        Modifier.offset {
            val offset = if (isKeyboardVisible) {
                paddingValue.roundToPx()
            } else {
                0
            }
            IntOffset(0, -offset)
        }
    )
}

fun Modifier.testTagIBank(tag: String): Modifier {
    return runCatching {
        if (Utils.flavor != "prod") this.testTag(tag) else this
    }.getOrDefault(this)
}