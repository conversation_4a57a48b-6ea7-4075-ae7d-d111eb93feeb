package vn.com.bidv.designsystem.component.feedback.otpview

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

@Composable
fun OtpItemInput(
    modifier: Modifier = Modifier,
    number: String = "",
    isSelected: Boolean = false,
    textStyle: TextStyle = LocalTypography.current.headlineHeadline_s,
) {
    val localScheme = LocalColorScheme.current
    Box(
        modifier = modifier
            .shadow(
                elevation = 0.dp,
                spotColor = localScheme.borderBrandPrimary,
                ambientColor = localScheme.borderBrandPrimary
            )
            .border(
                width = 1.dp,
                color = if (isSelected) {
                    localScheme.bgBrand_01Primary
                } else {
                    localScheme.borderMainSecondary
                },
                shape = RoundedCornerShape(8.dp)
            )
            .clip(shape = RoundedCornerShape(8.dp))
            .padding(IBSpacing.spacingS),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = number,
            style = textStyle,
            color = localScheme.contentMainPrimary,
        )
    }
}