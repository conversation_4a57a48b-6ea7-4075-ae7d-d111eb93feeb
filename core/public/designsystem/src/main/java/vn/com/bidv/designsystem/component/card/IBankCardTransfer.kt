package vn.com.bidv.designsystem.component.card

import IBGradient
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.datadisplay.badge.IBankBadgeLabel
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelColor
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelSize
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelType
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.IBSpacing.spacingM
import vn.com.bidv.designsystem.theme.IBSpacing.spacingS
import vn.com.bidv.designsystem.theme.IBSpacing.spacingXs
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

@Composable
fun IBankCardTransfer(
    modifier: Modifier = Modifier.fillMaxWidth(),
    icon: (@Composable () -> Unit)? = null,
    title: String,
    subTitle: String? = null,
    tag: (@Composable () -> Unit)? = null,
    listDescription: List<String>? = null,
    bgBrush: Brush = IBGradient.color_grd_card_primary,
    isShowLogo: Boolean = true,
    isShowDropDownIcon: Boolean = true,
    onClick: () -> Unit = {}
) {

    val colorSchema = LocalColorScheme.current
    val typography = LocalTypography.current
    var iconHeight by remember { mutableStateOf(0) }


    Box(
        modifier = modifier
            .clip(RoundedCornerShape(IBCornerRadius.cornerRadiusL))
            .wrapContentHeight()
            .background(brush = bgBrush)
            .padding(start = 0.dp, top = 0.dp, bottom = 0.dp, end = 0.dp)
            .clickable { onClick() }) {
        if (isShowLogo) {
            Icon(
                imageVector = ImageVector.vectorResource(id = R.drawable.bidv_flower_background_transfer),
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .onGloballyPositioned { coordinates ->
                        iconHeight = coordinates.size.height
                    },
                contentDescription = "Account Icon",
                tint = Color.Unspecified
            )
        }

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(min = with(LocalDensity.current) { iconHeight.toDp() })
                .padding(start = spacingM, top = spacingS, bottom = spacingS, end = spacingM),
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (icon != null) {
                    Box(modifier = Modifier.size(32.dp)) {
                        icon()
                    }
                    Spacer(modifier = Modifier.width(spacingXs))
                }
                Text(
                    color = colorSchema.contentOn_specialPrimary,
                    text = title,
                    style = typography.labelLabel_l
                )
                Spacer(modifier = Modifier.weight(1f))
                if (isShowDropDownIcon) {
                    Icon(
                        modifier = Modifier.size(20.dp),
                        painter = painterResource(id = R.drawable.circle_arrow_down_outline),
                        tint = colorSchema.contentOn_specialPrimary,
                        contentDescription = ""
                    )
                }
            }
            Column(Modifier.fillMaxWidth()) {
                Spacer(modifier = Modifier.height(spacingXs))
                Row {
                    Text(
                        color = colorSchema.contentOn_specialPrimary,
                        text = subTitle ?: "",
                        style = typography.titleTitle_m,
                    )
                    Spacer(modifier = Modifier.width(IBSpacing.spacing2xs))
                    if (tag != null) {
                        tag()
                    }
                }

                listDescription?.forEachIndexed { index, description ->
                    if (index != 0) {
                        Spacer(modifier = Modifier.height(IBSpacing.spacing3xs))
                    }
                    Text(
                        color = colorSchema.contentOn_specialSecondary,
                        text = description,
                        style = typography.bodyBody_m
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ShowPreviewCardTransfer() {
    Column(
        Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {

        IBankCardTransfer(
            title = "BIDV",
            icon = {
                Image(painter = painterResource(id = R.mipmap.bidv), contentDescription = "bidv")
            },
            subTitle = "***********",
            tag = {
                IBankBadgeLabel(
                    title = "Mặc định",
                    badgeSize = LabelSize.SM,
                    badgeColor = LabelColor.ON_BRAND,
                    badgeType = LabelType.ROUNDED,
                )
            },
            listDescription = listOf("CÔNG TY TNHH HOA"),
        )
        Spacer(modifier = Modifier.height(spacingM))

        IBankCardTransfer(
            title = "BIDV",
            icon = {
                Image(painter = painterResource(id = R.mipmap.bidv), contentDescription = "bidv")
            },
            subTitle = "***********",
            tag = {
                IBankBadgeLabel(
                    title = "Mặc định",
                    badgeSize = LabelSize.SM,
                    badgeColor = LabelColor.ON_BRAND,
                    badgeType = LabelType.ROUNDED,
                )
            },
            listDescription = listOf("CONG TY TNHH HOA  CONG TY TNHH HOA CONG TY TNHH HOA CONG TY TNHH HOACONG TY TNHH HOACONG TY TNHH HOACONG TY TNHH HOA CONG TY TNHH HOA CONG TY TNHH HOA"),
        )

        Spacer(modifier = Modifier.height(spacingM))

        IBankCardTransfer(
            title = "VPBANK",
            icon = {
                Image(painter = painterResource(id = R.mipmap.vpbank), contentDescription = "bidv")
            },
            subTitle = "***********",
            listDescription = listOf(
                "NGUYEN VAN A",
                "Cấp ngày: 12/12/2010",
                "Cuc canh sat quan ly hanh chinh ve trat tu xa hoi"
            ), bgBrush = IBGradient.color_grd_card_nh, isShowLogo = false
        )

        Spacer(modifier = Modifier.height(spacingM))

        IBankCardTransfer(
            title = "VPBANK",
            icon = {
                Image(painter = painterResource(id = R.mipmap.vpbank), contentDescription = "bidv")
            },
            subTitle = "***********",
            listDescription = listOf(
                "NGUYEN VAN A"
            ), bgBrush = IBGradient.color_grd_card_nh, isShowLogo = false
        )
    }
}