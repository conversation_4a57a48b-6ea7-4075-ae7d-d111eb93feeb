package vn.com.bidv.designsystem.component.feedback.bottomsheet

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.union
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.SheetState
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBColorScheme
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.IBTypography
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun IBankBottomSheet(
    title: String? = null,
    closeIcon: ImageVector? = ImageVector.vectorResource(id = R.drawable.close),
    onDismiss: (() -> Unit)? = {},
    sheetState: SheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true),
    dragHandle: @Composable (() -> Unit)? = null,
    applyMinHeight: Boolean = true,
    bottomSheetContent: @Composable () -> Unit,
) {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current
    val scope = rememberCoroutineScope()

    val systemBarsInsets = WindowInsets.systemBars.union(WindowInsets.ime)
    val navBarHeight = with(LocalDensity.current) { systemBarsInsets.getBottom(this).toDp() }
    val statusBarHeight = WindowInsets.statusBars.asPaddingValues().calculateTopPadding()

    val configuration = LocalConfiguration.current
    val screenHeight = configuration.screenHeightDp.dp
    val availableHeight = screenHeight - (navBarHeight * 0.85f) - statusBarHeight
    val thresholdHeightPx = with(LocalDensity.current) { (availableHeight * 0.7f).toPx() }
    val defaultScreenHeight = (screenHeight * 0.3f)

    var contentHeightPx by remember { mutableFloatStateOf(0f) }

    ModalBottomSheet(
        modifier = Modifier.testTagIBank("IBankBottomSheet_$title"),
        onDismissRequest = {
            onDismiss?.invoke()
        },
        sheetState = sheetState,
        dragHandle = dragHandle,
        shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL),
        containerColor = colorScheme.bgMainTertiary,
    ) {

        val keyboardController = LocalSoftwareKeyboardController.current
        val focusManager = LocalFocusManager.current
        Box(modifier = Modifier
            .pointerInput(Unit) {
                detectTapGestures(
                    onTap = {
                        keyboardController?.hide()
                        // clear current focus
                        focusManager.clearFocus()
                    }
                )
            }
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(
                        max = if (contentHeightPx > thresholdHeightPx) {
                            availableHeight
                        } else {
                            thresholdHeightPx.dp
                        },
                        min = if (applyMinHeight) defaultScreenHeight else Dp.Unspecified
                    )
                    .onGloballyPositioned { coordinates ->
                        contentHeightPx = coordinates.size.height.toFloat()
                    }
            ) {
                IBankBottomSheetAppBar(title, colorScheme, typography, closeIcon) {
                    scope.launch {
                        sheetState.hide()
                    }.invokeOnCompletion {
                        onDismiss?.invoke()
                    }
                }

                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .animateContentSize()
                        .weight(weight = 1f, fill = false)
                ) {
                    bottomSheetContent()
                }
            }
        }
    }
}

@Composable
private fun IBankBottomSheetAppBar(
    title: String?,
    colorScheme: IBColorScheme,
    typography: IBTypography,
    closeIcon: ImageVector?,
    onCloseClick: (() -> Unit)
) {
    if (closeIcon == null && title == null) {
        return
    }
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                horizontal = IBSpacing.spacingS,
                vertical = IBSpacing.spacing2xs,
            )
            .heightIn(min = IBSpacing.spacing5xl),
        verticalAlignment = Alignment.CenterVertically
    ) {
        title?.let {
            Text(
                text = it,
                modifier = Modifier
                    .weight(1f)
                    .padding(horizontal = IBSpacing.spacing2xs),
                color = colorScheme.contentMainPrimary,
                style = typography.titleTitle_m
            )
        } ?: Spacer(modifier = Modifier.weight(1f))
        closeIcon?.let {
            IconButton(
                modifier = Modifier
                    .size(IBSpacing.spacing3xl),
                onClick = {
                    onCloseClick()
                },
            ) {
                Icon(
                    imageVector = it,
                    contentDescription = stringResource(id = vn.com.bidv.localization.R.string.close),
                    modifier = Modifier
                        .size(IBSpacing.spacingL)
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
@Preview
fun IBankBottomSheetPreview() {
    val sampleData1 = (0..3).map { "Tài khoản $it" to "**********$it" }
    val sampleData2 = (0..9).map { "Tài khoản $it" to "**********$it" }
    val sampleData3 = (0..21).map { "Tài khoản $it" to "**********$it" }

    var dataBottomSheet by remember { mutableStateOf(emptyList<Pair<String, String>>()) }
    var isLoading by remember { mutableStateOf(false) }
    var isLoading2 by remember { mutableStateOf(false) }
    var isLoading3 by remember { mutableStateOf(false) }
    var showSheet by remember { mutableStateOf(false) }

    LaunchedEffect(isLoading) {
        delay(2000L)
        dataBottomSheet = sampleData1
        isLoading = false
    }
    LaunchedEffect(isLoading2) {
        delay(2000L)
        dataBottomSheet = sampleData2
        isLoading2 = false
    }
    LaunchedEffect(isLoading3) {
        delay(2000L)
        dataBottomSheet = sampleData3
        isLoading3 = false
    }
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(32.dp)
    ) {

        Button(
            onClick = {
                showSheet = true
                isLoading = true
            },
            modifier = Modifier.padding(16.dp)
        ) {
            Text("Bottom Sheet Hug")
        }
        Button(
            onClick = {
                showSheet = true
                isLoading2 = true
            },
            modifier = Modifier.padding(16.dp)
        ) {
            Text("Bottom Sheet Partial")
        }
        Button(
            onClick = {
                showSheet = true
                isLoading3 = true
            },
            modifier = Modifier.padding(16.dp)
        ) {
            Text("Bottom Sheet Full")
        }
    }

    if (showSheet) {
        IBankBottomSheet(
            title = "Số tài khoản",
            onDismiss = { showSheet = false },
            bottomSheetContent = {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(min = 100.dp)
                ) {
                    if (isLoading || isLoading2 || isLoading3) {
                        CircularProgressIndicator(modifier = Modifier.align(Alignment.Center))
                    } else {
                        LazyColumn(
                            modifier = Modifier.padding(IBSpacing.spacingM),
                            verticalArrangement = Arrangement.spacedBy(IBSpacing.spacing3xs)
                        ) {
                            items(
                                count = dataBottomSheet.size,
                            ) { index ->
                                val item = dataBottomSheet[index]
                                Row(modifier = Modifier.heightIn(min = 50.dp)) {
                                    Text(text = item.first, modifier = Modifier.weight(1f))
                                    Text(text = item.second)
                                }
                            }
                        }
                    }
                }
            },
        )
    }
}