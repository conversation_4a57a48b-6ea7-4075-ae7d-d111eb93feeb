package vn.com.bidv.designsystem.component.datepicker

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import vn.com.bidv.designsystem.component.datepicker.model.BorderType
import vn.com.bidv.designsystem.component.datepicker.model.DatePickerConfig
import vn.com.bidv.designsystem.component.datepicker.utils.equalDay
import vn.com.bidv.designsystem.component.datepicker.utils.getBorderType
import vn.com.bidv.designsystem.component.datepicker.utils.getDate
import vn.com.bidv.designsystem.component.datepicker.utils.getDayNames
import vn.com.bidv.designsystem.component.datepicker.utils.getDaysInMonth
import vn.com.bidv.designsystem.component.datepicker.utils.getFirstDayOfMonth
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import java.util.Calendar
import java.util.Date
import vn.com.bidv.localization.R as RLocalization

@Composable
internal fun DayPicker(
    modifier: Modifier,
    startDate: Date?,
    endDate: Date?,
    displayedMonth: Int,
    displayedYear: Int,
    firstDayOfWeek: Int = Calendar.MONDAY,
    datePickerConfig: DatePickerConfig,
    onDateSelected: (Date) -> Unit
) {
    val calendar = Calendar.getInstance()
    calendar.firstDayOfWeek = firstDayOfWeek
    val today = Calendar.getInstance()
    Column(modifier = modifier) {
        Row(
            modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            val dayNames = getDayNames(stringArrayResource(RLocalization.array.days_of_week).toList(),firstDayOfWeek)
            dayNames.forEach { day ->
                Text(
                    text = day,
                    modifier = Modifier
                        .padding(8.dp)
                        .weight(1f)
                        .fillMaxWidth(),
                    textAlign = TextAlign.Center,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold
                )
            }
        }

        LazyVerticalGrid(
            columns = GridCells.Fixed(7),
            contentPadding = PaddingValues(8.dp),
            modifier = Modifier.fillMaxWidth()
        ) {
            val daysInCurrentMonth = getDaysInMonth(displayedMonth, displayedYear)
            val firstDayOfMonth = getFirstDayOfMonth(displayedMonth, displayedYear, firstDayOfWeek)
            val daysInPreviousMonth = getDaysInMonth(
                if (displayedMonth == Calendar.JANUARY) Calendar.DECEMBER else displayedMonth - 1,
                if (displayedMonth == Calendar.JANUARY) displayedYear - 1 else displayedYear
            )

            // Add padding days from the previous month
            val previousMonthPaddingDays = firstDayOfMonth
            items(previousMonthPaddingDays) { dayOffset ->
                val day = daysInPreviousMonth - previousMonthPaddingDays + dayOffset + 1
                var month = displayedMonth
                var year = displayedYear
                if (displayedMonth == Calendar.JANUARY) {
                    month = Calendar.DECEMBER
                    year -= 1
                }
                val dayDate = getDate(year, month, daysInCurrentMonth)
                val borderType = getBorderType(dayDate, startDate, endDate, firstDayOfWeek)
                DayItem(day = day,
                    isInRange = false,
                    isSelected = false,
                    isPaddingDay = true,
                    borderType = borderType,
                    onClick = {})
            }

            // Add current month's days
            items(daysInCurrentMonth) { index ->
                val day = index + 1
                val date = getDate(displayedYear, displayedMonth, day)
                val isToday =
                    displayedYear == today.get(Calendar.YEAR) && displayedMonth == today.get(
                        Calendar.MONTH
                    ) && day == today.get(Calendar.DAY_OF_MONTH)
                val dayDate = getDate(displayedYear, displayedMonth, day)
                val borderType = getBorderType(dayDate, startDate, endDate, firstDayOfWeek)
                val isPaddingDay = (datePickerConfig.maxDate != null && date.after(datePickerConfig.maxDate))
                        || (datePickerConfig.minDate != null && date.before(datePickerConfig.minDate))
                DayItem(day = day,
                    isInRange = startDate != null && endDate != null && (date.after(
                        startDate
                    ) || date == startDate) && (date.before(
                        endDate
                    ) || date == endDate),
                    isSelected = date.equalDay(startDate) || date.equalDay(endDate),
                    isPaddingDay = isPaddingDay,
                    isToday = isToday,
                    borderType = borderType,
                    onClick = {
                        onDateSelected(date)
                    })
            }

            // Add padding days for the next month to fill up to 42 items
            val totalDisplayedDays = previousMonthPaddingDays + daysInCurrentMonth
            val nextMonthPaddingDays = 6 * 7 - totalDisplayedDays
            items(nextMonthPaddingDays) { index ->
                val day = index + 1
                var month = displayedMonth
                var year = displayedYear
                if (displayedMonth == Calendar.DECEMBER) {
                    month = Calendar.JANUARY
                    year += 1
                }
                val dayDate = getDate(year, month, daysInCurrentMonth)
                val borderType = getBorderType(dayDate, startDate, endDate, firstDayOfWeek)
                DayItem(day = day,
                    isInRange = false,
                    isSelected = false,
                    isPaddingDay = true,
                    borderType = borderType,
                    onClick = {})
            }
        }
    }

}

@Composable
private fun DayItem(
    day: Int,
    isInRange: Boolean,
    isSelected: Boolean,
    isPaddingDay: Boolean,
    borderType: BorderType,
    isToday: Boolean = false,
    onClick: () -> Unit
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current

    val textAlpha =
        if (isPaddingDay) 0.3f else 1f // Lower alpha for padding days to create a faded effect

    Box(contentAlignment = Alignment.Center,
        modifier = Modifier
            .padding(vertical = 1.dp)
            .aspectRatio(1f) // Ensures a square shape
            .fillMaxWidth(1f / 7)
            .border(
                width = 1.dp, color = when {
                    isToday -> colorScheme.borderBrandQuaternary
                    else -> Color.Transparent
                }, shape = RoundedCornerShape(IBCornerRadius.cornerRadiusM)
            )
            .background(
                color = when {
                    isInRange -> colorScheme.bgBrand_01Tertiary
                    else -> Color.Transparent
                }, shape = when (borderType) {
                    BorderType.START -> RoundedCornerShape(topStart = 8.dp, bottomStart = 8.dp)
                    BorderType.FULL -> RoundedCornerShape(8.dp)
                    BorderType.NONE -> RoundedCornerShape(0.dp)
                    BorderType.END -> RoundedCornerShape(topEnd = 8.dp, bottomEnd = 8.dp)
                }
            )
            .clickable(enabled = !isPaddingDay) { onClick() }
            .alpha(textAlpha)
    ) {

        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight()
                .background(
                    color = when {
                        isSelected -> colorScheme.bgBrand_01Primary
                        else -> Color.Transparent
                    }, shape = RoundedCornerShape(IBCornerRadius.cornerRadiusM)
                )
        ) {
            Text(
                text = day.toString(),
                color = if (isSelected) colorScheme.contentOn_specialPrimary else colorScheme.contentMainPrimary,
                style = if (isSelected) typography.titleTitle_s else typography.bodyBody_m,
            )
        }

    }
}

@Preview
@Composable
fun PreviewDayPicker() {
    Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
        DayPicker(
            modifier = Modifier.padding(innerPadding),
            startDate = null,
            endDate = null,
            displayedMonth = Calendar.getInstance().get(Calendar.MONTH),
            displayedYear = Calendar.getInstance().get(Calendar.YEAR),
            firstDayOfWeek = Calendar.MONDAY,
            datePickerConfig = DatePickerConfig.build(),
        ) {

        }
    }
}