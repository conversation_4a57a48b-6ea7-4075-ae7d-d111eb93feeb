package vn.com.bidv.designsystem.ui.contentloader

import vn.com.bidv.common.patterns.mvi.BaseMviViewModel
import vn.com.bidv.common.patterns.mvi.BaseState

abstract class ContentLoaderViewModel<T>(
    reducer: ContentLoaderReducer<T>
) : BaseMviViewModel<ContentLoaderReducer.ContentLoaderViewState<T>, ContentLoaderReducer.ContentLoaderViewEvent<T>, ContentLoaderReducer.ContentLoaderSideEffect>(
    initialState = ContentLoaderReducer.ContentLoaderViewState.CommonState(BaseState.InitScreen),
    reducer = reducer
) {

    override fun handleEffect(
        sideEffect: ContentLoaderReducer.ContentLoaderSideEffect,
        onResult: (ContentLoaderReducer.ContentLoaderViewEvent<T>) -> Unit
    ) {
        if (sideEffect is ContentLoaderReducer.ContentLoaderSideEffect.GetDataStatus) {
            fetchData(
                onLoadSuccess = { data ->
                    onResult(ContentLoaderReducer.ContentLoaderViewEvent.GetDataSuccess(data))
                },
                onLoadFail = { errorMessage ->
                    onResult(ContentLoaderReducer.ContentLoaderViewEvent.GetDataFail(errorMessage))
                }
            )
        }
    }

    abstract fun fetchData(
        onLoadSuccess: (T) -> Unit,
        onLoadFail: (String?) -> Unit
    )
}
