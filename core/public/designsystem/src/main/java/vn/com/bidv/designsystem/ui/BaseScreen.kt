package vn.com.bidv.designsystem.ui

import android.app.Activity
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.core.view.WindowInsetsControllerCompat
import androidx.navigation.NavController
import vn.com.bidv.common.patterns.mvi.BaseMviViewModel
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.common.ui.BaseMVIScreen
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankTopAppBar
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.common.patterns.mvi.SideEffect as MVISideEffect

/**
 * A composable function that wraps around the BaseScreen to provide a base structure
 * for handling common MVI (Model-View-Intent) patterns in a Composable UI.
 *
 * @param UiState represents the View state.
 * @param UiEvent represents UI events that trigger state changes.
 * @param SideEffect represents side effects, such as navigation or showing a toast.
 * @param navController the NavController used for navigation.
 * @param viewModel holds the current UI state, events, and side effects.
 * @param renderContent a composable function that renders the main content based on the current UI state.
 * @param handleSideEffect handles side effects emitted by the ViewModel, typically for one-time actions like navigation or showing messages.
 * @param topAppBarConfig config for show TopAppBar.
 */
@Composable
fun <UiState : ViewState, UiEvent : ViewEvent, SideEffect : MVISideEffect> BaseScreen(
    navController: NavController,
    viewModel: BaseMviViewModel<UiState, UiEvent, SideEffect>,
    handleSideEffect:  ((SideEffect) -> Unit)? = null,
    topAppBarType: TopAppBarType = TopAppBarType.Title,
    topAppBarConfig: TopAppBarConfig = TopAppBarConfig(),
    isLightStatusBar: Boolean = true,
    backgroundColor: Color = LocalColorScheme.current.bgMainSecondary,
    renderContent: @Composable (UiState, (UiEvent) -> Unit) -> Unit,
) {
    val activity = LocalContext.current as? Activity
    activity?.window?.let {
        val controller = WindowInsetsControllerCompat(it, it.decorView)
        controller.isAppearanceLightStatusBars = isLightStatusBar
    }
    BaseMVIScreen(
        viewModel = viewModel,
        renderContent = { uiState, onEvent ->
            Scaffold(
                topBar = {
                    if (topAppBarConfig.isShowTopAppBar) IBankTopAppBar(navController, topAppBarType, topAppBarConfig)
                },
                contentWindowInsets = WindowInsets(0.dp, 0.dp, 0.dp, 0.dp),
                containerColor = backgroundColor
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(it)
                        .padding(WindowInsets.navigationBars.asPaddingValues())
                ) {
                    renderContent(uiState, onEvent)
                }
            }
        },
        handleSideEffect = handleSideEffect
    )
}