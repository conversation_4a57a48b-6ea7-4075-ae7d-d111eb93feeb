package vn.com.bidv.designsystem.component.datadisplay.avatar

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.times

@Composable
fun IBankAvatarGroup(
    listInfo: List<AvatarInfo> = emptyList(),
) {
    val overlap: Dp = 36.dp
    Box(
        Modifier
            .width(listInfo.size * overlap + 12.dp)
    ) {
        listInfo.forEachIndexed { index, info ->
            IBankAvatar(
                modifier = Modifier.offset(x = (index * overlap)),
                info
            )

        }
    }
}

@Preview
@Composable
fun AvatarGroupSample() {

    Column(
        modifier = Modifier
            .wrapContentWidth()
            .padding(10.dp),
        verticalArrangement = Arrangement.spacedBy(20.dp)
    ) {
        IBankAvatarGroup(
            listInfo = listOf(
                AvatarInfo(
                    "",
                    ""
                ),
                AvatarInfo(
                    "Cirdan Dear",
                    ""
                ),
                AvatarInfo(
                    "",
                    "https://avatar.iran.liara.run/public/40"
                ),
                AvatarInfo(
                    "Elf Format",
                    "https://avatar.iran.liara.run/public/40"
                ),
                AvatarInfo(
                    "",
                    "https://avatar.iran.liara.run/public/41"
                ),
                AvatarInfo(
                    "",
                    "https://avatar.iran.liara.run/public/42"
                )
            )
        )
    }

}