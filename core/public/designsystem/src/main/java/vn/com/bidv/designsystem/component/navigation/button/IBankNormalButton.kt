package vn.com.bidv.designsystem.component.navigation.button

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.NoRippleEffectView
import vn.com.bidv.designsystem.theme.IBColorScheme
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBShadow
import vn.com.bidv.designsystem.theme.IBShadow.dropShadow
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.IBTypography
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.utils.debounceClick

/**
 * Draft version of Normal Buttons with all type and size.
 * @param onClick called when this button is clicked.
 * @param modifier the custom [Modifier] to be applied to this button.
 * @param isEnable controls the enabled state of this button. When `false`, this component will not
 *   respond to user input, and it will appear visually disabled and disabled to accessibility
 *   services.
 * @param text defines text content, when text is empty, only use one icon between leading or trailing icon
 * to display a icon button.
 */

@Composable
fun IBankNormalButton(
    modifier: Modifier = Modifier,
    type: NormalButtonType = NormalButtonType.PRIMARY(LocalColorScheme.current),
    size: NormalButtonSize = NormalButtonSize.L(LocalTypography.current),
    isEnable: Boolean = true,
    text: String? = null,
    leadingIcon: ImageVector? = null,
    trailingIcon: ImageVector? = null,
    onClick: () -> Unit
) {

    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()

    var backgroundColor by remember { mutableStateOf(Color.Transparent) }
    var borderColor by remember { mutableStateOf(Color.Transparent) }
    var textColor by remember { mutableStateOf(Color.Transparent) }

    val onlyImage = text.isNullOrEmpty()

    if (isEnable) {
        if (!isPressed) {
            backgroundColor = type.backgroundColorUnPress
            borderColor = type.borderColorUnPress
        } else {
            backgroundColor = type.backgroundColorPress
            borderColor = type.borderColorPress
        }
    } else {
        backgroundColor = type.backgroundColorDisable
        borderColor = type.borderColorDisable
    }

    textColor = if (isEnable) {
        type.textColorEnable
    } else {
        type.textColorDisable
    }
    val buttonModifier: Modifier = modifier
        .height(size.height)
        .then(
            if (onlyImage) {
                Modifier.width(size.height)
            } else {
                Modifier
            }
        )
        .then(
            if (type is NormalButtonType.FLOATING) {
                Modifier
                    .dropShadow(
                        config = IBShadow.shadowsShadowm0.copy(
                            shape = RoundedCornerShape(
                                IBCornerRadius.cornerRadiusRound
                            )
                        )
                    )
                    .dropShadow(
                        config = IBShadow.shadowsShadowm1.copy(
                            shape = RoundedCornerShape(
                                IBCornerRadius.cornerRadiusRound
                            )
                        )
                    )
            } else {
                Modifier
            }
        )

    NoRippleEffectView {
        Button(
            modifier = buttonModifier,
            onClick = debounceClick(onClick = onClick),
            colors = ButtonDefaults.buttonColors(backgroundColor),
            border = BorderStroke(1.dp, borderColor),
            shape = RoundedCornerShape(type.rounder),
            interactionSource = interactionSource,
            contentPadding = PaddingValues(0.dp)
        ) {

            Row(
                modifier = Modifier.then(
                    if (!onlyImage) {
                        Modifier.padding(
                            horizontal = size.paddingH,
                            vertical = size.paddingV
                        )
                    } else {
                        Modifier
                    }
                ),
                horizontalArrangement = Arrangement.spacedBy(
                    space = size.paddingText,
                    Alignment.CenterHorizontally
                ),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                leadingIcon?.let {
                    Icon(
                        imageVector = it,
                        contentDescription = null,
                        tint = textColor,
                        modifier = Modifier
                            .size(size.iconSize)
                    )
                }

                text?.let {
                    Text(
                        modifier = Modifier.testTag("IBankNormalButton_$text"),
                        text = text,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        color = textColor,
                        style = size.typography,
                        textAlign = TextAlign.Center
                    )
                }

                trailingIcon?.let {
                    Icon(
                        imageVector = it,
                        contentDescription = null,
                        tint = textColor,
                        modifier = Modifier.size(size.iconSize)
                    )
                }
            }

        }
    }

}

sealed class NormalButtonSize(
    val height: Dp,
    val iconSize: Dp,
    val paddingH: Dp,
    val paddingV: Dp,
    val paddingText: Dp,
    val typography: TextStyle
) {

    class SM(typo: IBTypography) : NormalButtonSize(
        height = 32.dp,
        iconSize = 20.dp,
        paddingH = IBSpacing.spacingS,
        paddingV = IBSpacing.spacingXs,
        paddingText = IBSpacing.spacing2xs,
        typography = typo.labelLabel_l
    )

    class M(typo: IBTypography) : NormalButtonSize(
        height = 40.dp,
        iconSize = 20.dp,
        paddingH = IBSpacing.spacingM,
        paddingV = IBSpacing.spacingXs,
        paddingText = IBSpacing.spacing2xs + IBSpacing.spacing3xs,
        typography = typo.labelLabel_xl
    )

    class L(typo: IBTypography) : NormalButtonSize(
        height = 48.dp,
        iconSize = 24.dp,
        paddingH = IBSpacing.spacingM,
        paddingV = IBSpacing.spacingS,
        paddingText = IBSpacing.spacing2xs + IBSpacing.spacing2xs,
        typography = typo.labelLabel_xl
    )
}

sealed class NormalButtonType(
    val backgroundColorUnPress: Color,
    val backgroundColorPress: Color,
    val backgroundColorDisable: Color,
    val borderColorUnPress: Color,
    val borderColorPress: Color,
    val borderColorDisable: Color,
    val textColorEnable: Color,
    val textColorDisable: Color,
    val rounder: Dp,
) {

    class PRIMARY(colorScheme: IBColorScheme) :
        NormalButtonType(
            backgroundColorUnPress = colorScheme.bgBrand_01Primary,
            backgroundColorPress = colorScheme.bgBrand_01Primary_press,
            backgroundColorDisable = colorScheme.bgDisablePrimary,
            borderColorUnPress = colorScheme.bgBrand_01Primary,
            borderColorPress = colorScheme.bgBrand_01Primary_press,
            borderColorDisable = colorScheme.borderDisablePrimary,
            textColorEnable = colorScheme.contentOn_specialPrimary,
            textColorDisable = colorScheme.contentDisablePrimary,
            rounder = IBCornerRadius.cornerRadiusL,
        )

    class FLOATING(colorScheme: IBColorScheme) :
        NormalButtonType(
            backgroundColorUnPress = colorScheme.bgBrand_01Primary,
            backgroundColorPress = colorScheme.bgBrand_01Primary_press,
            backgroundColorDisable = colorScheme.bgDisablePrimary,
            borderColorUnPress = colorScheme.bgBrand_01Primary,
            borderColorPress = colorScheme.bgBrand_01Primary_press,
            borderColorDisable = colorScheme.borderDisablePrimary,
            textColorEnable = colorScheme.contentOn_specialPrimary,
            textColorDisable = colorScheme.contentDisablePrimary,
            rounder = IBCornerRadius.cornerRadiusRound,
        )

    class SECONDARYGRAY(colorScheme: IBColorScheme) :
        NormalButtonType(
            backgroundColorUnPress = colorScheme.bgMainTertiary,
            backgroundColorPress = colorScheme.bgMainTertiary_press,
            backgroundColorDisable = colorScheme.bgMainTertiary,
            borderColorUnPress = colorScheme.borderMainPrimary,
            borderColorPress = colorScheme.borderMainPrimary_press,
            borderColorDisable = colorScheme.borderDisablePrimary,
            textColorEnable = colorScheme.contentMainPrimary,
            textColorDisable = colorScheme.contentDisablePrimary,
            rounder = IBCornerRadius.cornerRadiusL,
        )

    class SECONDARYCOLOR(colorScheme: IBColorScheme) :
        NormalButtonType(
            backgroundColorUnPress = colorScheme.bgMainTertiary,
            backgroundColorPress = colorScheme.bgBrand_01Tertiary_press,
            backgroundColorDisable = colorScheme.bgMainTertiary,
            borderColorUnPress = colorScheme.borderBrandPrimary,
            borderColorPress = colorScheme.borderBrandPrimary_press,
            borderColorDisable = colorScheme.borderDisablePrimary,
            textColorEnable = colorScheme.contentBrand_01Primary,
            textColorDisable = colorScheme.contentDisablePrimary,
            rounder = IBCornerRadius.cornerRadiusL,
        )

    class TERTIARY(colorScheme: IBColorScheme) :
        NormalButtonType(
            backgroundColorUnPress = Color.Transparent,
            backgroundColorPress = colorScheme.bgMainQuaternary_press,
            backgroundColorDisable = Color.Transparent,
            borderColorUnPress = Color.Transparent,
            borderColorPress = Color.Transparent,
            borderColorDisable = Color.Transparent,
            textColorEnable = colorScheme.contentMainSecondary,
            textColorDisable = colorScheme.contentDisablePrimary,
            rounder = IBCornerRadius.cornerRadiusL,
        )

    class DESPRIMARY(colorScheme: IBColorScheme) :
        NormalButtonType(
            backgroundColorUnPress = colorScheme.bgNegativePrimary,
            backgroundColorPress = colorScheme.bgNegativePrimary_press,
            backgroundColorDisable = colorScheme.bgDisablePrimary,
            borderColorUnPress = Color.Transparent,
            borderColorPress = Color.Transparent,
            borderColorDisable = colorScheme.borderDisablePrimary,
            textColorEnable = colorScheme.contentOn_specialPrimary,
            textColorDisable = colorScheme.contentDisablePrimary,
            rounder = IBCornerRadius.cornerRadiusL,
        )

    class DESSECONDARYCOLOR(colorScheme: IBColorScheme) :
        NormalButtonType(
            backgroundColorUnPress = colorScheme.bgMainTertiary,
            backgroundColorPress = colorScheme.bgNegativeSecondary_press,
            backgroundColorDisable = colorScheme.bgDisablePrimary,
            borderColorUnPress = colorScheme.borderNegativePrimary,
            borderColorPress = colorScheme.borderNegativePrimary_press,
            borderColorDisable = colorScheme.borderDisablePrimary,
            textColorEnable = colorScheme.contentNegativePrimary,
            textColorDisable = colorScheme.contentDisablePrimary,
            rounder = IBCornerRadius.cornerRadiusL,
        )

    class DESTERTIARY(colorScheme: IBColorScheme) :
        NormalButtonType(
            backgroundColorUnPress = Color.Transparent,
            backgroundColorPress = colorScheme.bgNegativeSecondary_press,
            backgroundColorDisable = Color.Transparent,
            borderColorUnPress = Color.Transparent,
            borderColorPress = Color.Transparent,
            borderColorDisable = Color.Transparent,
            textColorEnable = colorScheme.contentNegativePrimary,
            textColorDisable = colorScheme.contentDisablePrimary,
            rounder = IBCornerRadius.cornerRadiusL,
        )

    class NONOPAQUE(colorScheme: IBColorScheme) :
        NormalButtonType(
            backgroundColorUnPress = colorScheme.bgMainQuaternary_hover,
            backgroundColorPress = colorScheme.bgMainTertiary_press,
            backgroundColorDisable = colorScheme.bgMainTertiary,
            borderColorUnPress = Color.Transparent,
            borderColorPress = Color.Transparent,
            borderColorDisable = colorScheme.borderDisablePrimary,
            textColorEnable = colorScheme.contentMainSecondary,
            textColorDisable = colorScheme.contentDisablePrimary,
            rounder = IBCornerRadius.cornerRadiusL,
        )
}

@Composable
@Preview
fun ButtonExample() {

    Column(
        modifier = Modifier
            .padding(20.dp),
        verticalArrangement = Arrangement.spacedBy(20.dp)
    ) {

        IBankNormalButton(
            modifier = Modifier,
            size = NormalButtonSize.L(LocalTypography.current),
            type = NormalButtonType.PRIMARY(LocalColorScheme.current),
            text = "Button DEFAULT",
            leadingIcon = ImageVector.vectorResource(id = R.drawable.information_circle),
            trailingIcon = ImageVector.vectorResource(id = R.drawable.information_circle)
        ) { }
        IBankNormalButton(
            modifier = Modifier,
            size = NormalButtonSize.L(LocalTypography.current),
            type = NormalButtonType.FLOATING(LocalColorScheme.current),
            text = "Button FLOATING",
            leadingIcon = ImageVector.vectorResource(id = R.drawable.information_circle),
            trailingIcon = ImageVector.vectorResource(id = R.drawable.information_circle)
        ) { }
        IBankNormalButton(
            modifier = Modifier,
            size = NormalButtonSize.L(LocalTypography.current),
            type = NormalButtonType.FLOATING(LocalColorScheme.current),
            isEnable = false,
            text = "Button FLOATING DISABLE",
            leadingIcon = ImageVector.vectorResource(id = R.drawable.information_circle),
            trailingIcon = ImageVector.vectorResource(id = R.drawable.information_circle)
        ) { }
        IBankNormalButton(
            modifier = Modifier,
            size = NormalButtonSize.L(LocalTypography.current),
            type = NormalButtonType.FLOATING(LocalColorScheme.current),
            text = null,
            leadingIcon = ImageVector.vectorResource(id = R.drawable.information_circle),
        ) { }
        IBankNormalButton(
            modifier = Modifier,
            size = NormalButtonSize.M(LocalTypography.current),
            type = NormalButtonType.PRIMARY(LocalColorScheme.current),
            isEnable = false,
            text = "Button PRIMARY DISABLE",
            leadingIcon = ImageVector.vectorResource(id = R.drawable.information_circle),
            trailingIcon = ImageVector.vectorResource(id = R.drawable.information_circle)
        ) { }
        IBankNormalButton(
            modifier = Modifier,
            size = NormalButtonSize.SM(LocalTypography.current),
            type = NormalButtonType.SECONDARYCOLOR(LocalColorScheme.current),
            isEnable = false,
            text = "Button SECONDARYCOLOR DISABLE",
            leadingIcon = ImageVector.vectorResource(id = R.drawable.information_circle),
            trailingIcon = ImageVector.vectorResource(id = R.drawable.information_circle)
        ) { }
        IBankNormalButton(
            modifier = Modifier.fillMaxWidth(),
            size = NormalButtonSize.L(LocalTypography.current),
            type = NormalButtonType.SECONDARYGRAY(LocalColorScheme.current),
            text = "Button SECONDARYGRAY",
            leadingIcon = ImageVector.vectorResource(id = R.drawable.information_circle),
            trailingIcon = ImageVector.vectorResource(id = R.drawable.information_circle)
        ) { }
        IBankNormalButton(
            modifier = Modifier,
            size = NormalButtonSize.M(LocalTypography.current),
            type = NormalButtonType.DESPRIMARY(LocalColorScheme.current),
            text = "Button DESPRIMARY",
            leadingIcon = ImageVector.vectorResource(id = R.drawable.circle_arrow_left_outline),
            trailingIcon = ImageVector.vectorResource(id = R.drawable.circle_arrow_right_outline),
        ) { }
        IBankNormalButton(
            modifier = Modifier,
            size = NormalButtonSize.SM(LocalTypography.current),
            type = NormalButtonType.DESSECONDARYCOLOR(LocalColorScheme.current),
            text = "Button DESSECONDARYCOLOR",
            trailingIcon = ImageVector.vectorResource(id = R.drawable.circle_arrow_left_outline),
        ) { }
        IBankNormalButton(
            modifier = Modifier
                .wrapContentSize()
                .width(84.dp)
                .height(32.dp),
            size = NormalButtonSize.SM(LocalTypography.current),
            type = NormalButtonType.PRIMARY(LocalColorScheme.current),
            text = "Bo chon"
        ) { }
        IBankNormalButton(
            modifier = Modifier,
            size = NormalButtonSize.L(LocalTypography.current),
            type = NormalButtonType.DESSECONDARYCOLOR(LocalColorScheme.current),
            text = null,
            leadingIcon = ImageVector.vectorResource(id = R.drawable.information_circle),
        ) { }
        IBankNormalButton(
            modifier = Modifier,
            size = NormalButtonSize.SM(LocalTypography.current),
            type = NormalButtonType.PRIMARY(LocalColorScheme.current),
            text = null,
            trailingIcon = ImageVector.vectorResource(id = R.drawable.information_circle)
        ) { }

    }

}

