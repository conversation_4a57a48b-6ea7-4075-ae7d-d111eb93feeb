package vn.com.bidv.designsystem.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.icon.IBankDownloadIcon
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

/**
 * A composable function that displays either a downloadable image from a URL or a local drawable resource as the leading icon.
 *
 * @param modifier The modifier to be applied to the component layout.
 * @param isSelected Whether the item is currently selected. A checkmark icon is shown if true.
 * @param leadingIconUrl The URL of the leading icon image, if available.
 * @param defaultImgResId The resource ID of the fallback/default image when `leadingIconUrl` fails to load.
 * @param leadingIconResId The resource ID of a local drawable to use as the leading icon instead of a URL.
 * @param title The text to be displayed as the item's label.
 *
 * Note: Only one of [leadingIconUrl] or [leadingIconResId] should be used at a time
 */

@Composable
fun IBankContextMenu(
    modifier: Modifier = Modifier,
    isSelected: Boolean = false,
    leadingIconUrl: String? = null,
    defaultImgResId: Int = 0,
    leadingIconResId: Int? = null,
    title: String,
) {
    val colorScheme = LocalColorScheme.current
    Box(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .background(
                color = if (isSelected) colorScheme.bgBrand_01Tertiary else colorScheme.bgMainTertiary,
                shape = RoundedCornerShape(IBCornerRadius.cornerRadiusM)
            )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(
                    vertical = IBSpacing.spacingS,
                    horizontal = IBSpacing.spacingXs,
                ),
            verticalAlignment = Alignment.CenterVertically
        ) {
            leadingIconUrl?.let {
                IBankDownloadIcon(
                    modifier = Modifier.size(IBSpacing.spacing2xl),
                    iconUrl = leadingIconUrl,
                    defaultImgResId = defaultImgResId
                )
                Spacer(modifier = Modifier.width(IBSpacing.spacingS))
            }
            leadingIconResId?.let {
                Image(
                    modifier = Modifier.size(IBSpacing.spacing2xl),
                    painter = painterResource(id = leadingIconResId),
                    contentDescription = null
                )
                Spacer(modifier = Modifier.width(IBSpacing.spacingS))
            }
            Text(
                modifier = Modifier.weight(1f),
                text = title,
                style = LocalTypography.current.bodyBody_l,
                color = colorScheme.contentMainPrimary
            )
            if (isSelected) {
                Image(
                    painter = painterResource(id = R.drawable.check),
                    contentDescription = null,
                    modifier = Modifier.size(IBSpacing.spacingL)
                )
            }
        }
    }
}

@Preview
@Composable
fun PreviewIBankContextMenu() {
    IBankContextMenu(
        isSelected = true,
        leadingIconResId = R.drawable.tien_nuoc,
        title = "Example Service Type"
    )
}
