package vn.com.bidv.designsystem.ui.contentloader

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.BaseState
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState

class ContentLoaderReducer<T> :
    Reducer<ContentLoaderReducer.ContentLoaderViewState<T>, ContentLoaderReducer.ContentLoaderViewEvent<T>, ContentLoaderReducer.ContentLoaderSideEffect> {

    @Immutable
    sealed class ContentLoaderViewState<T> : ViewState {
        data class CommonState<T>(val baseState: BaseState) : ContentLoaderViewState<T>()
        data class ShowContent<T>(val data: T) : ContentLoaderViewState<T>()
    }

    @Immutable
    sealed class ContentLoaderViewEvent<T> : ViewEvent {
        class GetData<T> : ContentLoaderViewEvent<T>()
        data class GetDataSuccess<T>(val data: T) : ContentLoaderViewEvent<T>()
        data class GetDataFail<T>(val errorMessage: String? = null) : ContentLoaderViewEvent<T>()
    }

    @Immutable
    sealed class ContentLoaderSideEffect : SideEffect {
        data object GetDataStatus : ContentLoaderSideEffect()
    }

    override fun reduce(
        previousState: ContentLoaderViewState<T>,
        event: ContentLoaderViewEvent<T>,
    ): Pair<ContentLoaderViewState<T>, ContentLoaderSideEffect?> {
        return when (previousState) {
            is ContentLoaderViewState.CommonState -> {
                if (previousState.baseState is BaseState.InitScreen || previousState.baseState is BaseState.Error) {
                    if (event is ContentLoaderViewEvent.GetData) {
                        return ContentLoaderViewState.CommonState<T>(BaseState.Loading) to ContentLoaderSideEffect.GetDataStatus
                    }
                }

                if (previousState.baseState is BaseState.Loading) {
                    if (event is ContentLoaderViewEvent.GetDataFail) {
                        return ContentLoaderViewState.CommonState<T>(BaseState.Error(event.errorMessage)) to null
                    }
                    if (event is ContentLoaderViewEvent.GetDataSuccess) {
                        return ContentLoaderViewState.ShowContent(event.data) to null
                    }
                }

                previousState to null
            }

            is ContentLoaderViewState.ShowContent -> {
                previousState to null
            }
        }
    }
}
