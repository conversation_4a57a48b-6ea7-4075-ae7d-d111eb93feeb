package vn.com.bidv.designsystem.ui.contentloader

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.IBankEmptyState
import vn.com.bidv.designsystem.component.IBankLoaderIndicators
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.designsystem.ui.CommonScreen
import vn.com.bidv.localization.R

@Composable
fun <T> ContentLoaderBaseScreen(
    navController: NavHostController,
    viewModel: ContentLoaderViewModel<T>,
    topAppBarType: TopAppBarType = TopAppBarType.Title,
    topAppBarConfig: TopAppBarConfig = TopAppBarConfig(),
    backgroundColor: Color = LocalColorScheme.current.bgMainSecondary,
    loadingView: @Composable () -> Unit = { IBankLoaderIndicators() },
    onRetryWhenError: () -> Unit = { },
    errorView: @Composable (errorMessage: String?) -> Unit = { errorMessage ->
        IBankEmptyState(
            modifier = Modifier.fillMaxSize(),
            supportingText = errorMessage,
            textButton = stringResource(id = R.string.retry),
            onClickButton = onRetryWhenError
        )
    },
    contentView: @Composable (T) -> Unit
) {
    BaseScreen(
        navController = navController,
        viewModel = viewModel,
        renderContent = { uiState, onEvent ->
            when (uiState) {
                is ContentLoaderReducer.ContentLoaderViewState.CommonState -> {
                    CommonScreen(
                        baseState = uiState.baseState,
                        onInitScreen = {
                            onEvent(ContentLoaderReducer.ContentLoaderViewEvent.GetData())
                        },
                        loadingView = loadingView,
                        errorView = errorView,
                        onRetryWhenError = onRetryWhenError,
                    )
                }

                is ContentLoaderReducer.ContentLoaderViewState.ShowContent -> {
                    contentView(uiState.data)
                }
            }
        },
        topAppBarType = topAppBarType,
        topAppBarConfig = topAppBarConfig,
        backgroundColor = backgroundColor
    )
}
