package vn.com.bidv.designsystem.component.feedback.modelreason

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.window.Dialog
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.dataentry.IBFrameState
import vn.com.bidv.designsystem.component.dataentry.IBankFilterTextField
import vn.com.bidv.designsystem.component.dataentry.IBankInputFieldBase
import vn.com.bidv.designsystem.component.dataentry.RemoveSpecialCharacterFilterCNR
import vn.com.bidv.designsystem.component.navigation.button.IBankNormalButton
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonType
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.localization.R as RLocalization

@Composable
fun IBankModalReason(
    modifier: Modifier = Modifier,
    textValue: TextFieldValue? = null,
    labelLeft: String? = null,
    labelRight: String? = null,
    placeholderReason: String = "",
    maxLengthText: Int? = null,
    isShowTextCounter: Boolean = false,
    isTextArea: Boolean = false,
    isRequired: Boolean = false,
    isDismissRequestWhenClickButton: Boolean = true,
    title: String = stringResource(RLocalization.string.thong_bao),
    filters: List<IBankFilterTextField> = listOf(RemoveSpecialCharacterFilterCNR()),
    maxLineText: Int = 3,
    inputType: KeyboardOptions? = KeyboardOptions(keyboardType = KeyboardType.Text),
    onDismissRequest: () -> Unit = { },
    onClearTextInput: () -> Unit = { },
    state: IBFrameState = IBFrameState.DEFAULT(LocalColorScheme.current),
    helpTextLeft: String = "",
    helpTextRight: String = "",
    isAutoFocus: Boolean = false,
    onClickLeft: () -> Unit = { },
    onClickRight: () -> Unit = { },
    onTextInputChange: (TextFieldValue) -> Unit,
) {

    Dialog(
        onDismissRequest = {
            onDismissRequest()
        },
    ) {
        ModalContent(
            modifier = modifier,
            title = title,
            labelLeft = labelLeft,
            labelRight = labelRight,
            placeholderReason = placeholderReason,
            textValue = textValue,
            maxLengthText = maxLengthText,
            isShowTextCounter = isShowTextCounter,
            isTextArea = isTextArea,
            isDismissRequest = isDismissRequestWhenClickButton,
            isRequired = isRequired,
            maxLineText = maxLineText,
            onDismissRequest = {
                onDismissRequest()
            },
            onClearTextInput = {
                onClearTextInput()
            },
            state = state,
            helpTextLeft = helpTextLeft,
            helpTextRight = helpTextRight,
            onClickLeft = {
                onClickLeft()
            },
            isAutoFocus = isAutoFocus,
            onClickRight = {
                onClickRight()
            },
            onTextInputChange = {
                onTextInputChange(it)
            },
            filters = filters,
            inputType = inputType
        )
    }
}

@Composable
private fun ModalContent(
    modifier: Modifier,
    textValue: TextFieldValue? = null,
    title: String,
    labelLeft: String? = null,
    labelRight: String? = null,
    maxLengthText: Int? = null,
    isShowTextCounter: Boolean = false,
    isTextArea: Boolean = false,
    placeholderReason: String = "",
    isDismissRequest: Boolean = true,
    isRequired: Boolean = false,
    onDismissRequest: () -> Unit,
    onClearTextInput: () -> Unit,
    state: IBFrameState = IBFrameState.DEFAULT(LocalColorScheme.current),
    filters: List<IBankFilterTextField>,
    inputType: KeyboardOptions? = KeyboardOptions(keyboardType = KeyboardType.Text),
    helpTextLeft: String = "",
    helpTextRight: String = "",
    isAutoFocus: Boolean = false,
    maxLineText: Int,
    onClickLeft: () -> Unit = { },
    onClickRight: () -> Unit = { },
    onTextInputChange: (TextFieldValue) -> Unit,
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current

    Box(
        modifier = modifier,
        contentAlignment = Alignment.TopEnd
    ) {
        Surface(
            shape = RoundedCornerShape(IBSpacing.spacingXs),
            color = colorScheme.contentOn_specialPrimary
        ) {
            Column(
                modifier = Modifier
                    .wrapContentSize()
                    .padding(
                        top = IBSpacing.spacingM,
                        bottom = IBSpacing.spacingL,
                    )
            ) {

                Spacer(modifier = Modifier.height(IBSpacing.spacingXs))

                Text(
                    text = title,
                    style = typography.titleTitle_m,
                    modifier = Modifier.padding(start = IBSpacing.spacingL),
                )

                HorizontalDivider(
                    modifier = Modifier.padding(vertical = IBSpacing.spacingL),
                    color = colorScheme.borderMainSecondary,
                    thickness = IBBorderDivider.borderDividerS
                )

                Box(Modifier.padding(horizontal = IBSpacing.spacingL)) {
                    IBankInputFieldBase(
                        textValue = textValue,
                        required = true,
                        filters = filters,
                        placeholderText = placeholderReason,
                        maxLengthText = maxLengthText,
                        isShowMaxLength = isShowTextCounter,
                        isTextArea = isTextArea,
                        helpTextLeft = helpTextLeft,
                        helpTextRight = helpTextRight,
                        inputType = inputType,
                        state = state,
                        isAutoFocus = isAutoFocus,
                        maxLineText = maxLineText,
                        onClickClear = {
                            onClearTextInput()
                        }) {
                        onTextInputChange(it)
                    }
                }
                Spacer(modifier = Modifier.height(IBSpacing.spacingL))
                ActionButtons(
                    textValue = textValue,
                    labelLeft = labelLeft,
                    labelRight = labelRight,
                    onClickLeft = onClickLeft,
                    onClickRight = onClickRight,
                    isDismissRequest = isDismissRequest,
                    isRequired = isRequired,
                    onDismissRequest = onDismissRequest
                )
            }
        }

        IconButton(
            modifier = Modifier.padding(
                top = IBSpacing.spacingXs,
                end = IBSpacing.spacingXs
            ),
            onClick = onDismissRequest
        ) {
            Icon(
                painter = painterResource(R.drawable.modal_confirm_close),
                contentDescription = null
            )
        }
    }
}

@Composable
private fun ActionButtons(
    textValue: TextFieldValue? = null,
    labelLeft: String? = null,
    labelRight: String? = null,
    isRequired: Boolean = false,
    isDismissRequest: Boolean = true,
    onClickLeft: () -> Unit,
    onClickRight: () -> Unit,
    onDismissRequest: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = IBSpacing.spacingL),
        horizontalArrangement = Arrangement.spacedBy(
            space = IBSpacing.spacingM,
            Alignment.CenterHorizontally
        ),
        verticalAlignment = Alignment.CenterVertically
    ) {

        if (labelLeft != null) {
            IBankNormalButton(
                modifier = Modifier.weight(1f),
                type = NormalButtonType.SECONDARYGRAY(LocalColorScheme.current),
                text = labelLeft,
                onClick = {
                    if (isDismissRequest) onDismissRequest()
                    onClickLeft()
                },
            )
        }

        if (labelRight != null) {
            IBankNormalButton(
                modifier = Modifier.weight(1f),
                text = labelRight,
                onClick = {
                    if (isRequired && textValue?.text?.isEmpty() == true) {
                        // Không cho dismiss khi text empty
                    } else {
                        if (isDismissRequest) onDismissRequest()
                    }
                    onClickRight()
                }
            )
        }

    }

}

@Preview
@Composable
fun Preview() {

    var test by remember { mutableStateOf(TextFieldValue()) }
    IBankModalReason(
        title = "Từ chối giao dịch",
        labelLeft = "Hủy",
        labelRight = "Xác nhận",
        placeholderReason = "Lý do từ chối",
        textValue = test,
        onDismissRequest = { },
        onClickRight = {},
        onClearTextInput = { test = TextFieldValue("")},
    ){
        test = it
    }

}
