package vn.com.bidv.sdkbase.navigation.notifyroute

import kotlinx.serialization.Serializable

interface NotificationRouteBuilder {
    fun buildRoute(
        navigateId: String,
        defaultParamKeys: Set<String>? = null,
        listParam: List<NotiParam>? = null
    ): NotiRoute?
}

@Serializable
data class NotiRoute(val route: String, val listParam: List<Pair<String, String?>>)

@Serializable
data class NotiParam(val key: String, val value: String) : java.io.Serializable

class NotificationRouteBuilderDefault : NotificationRouteBuilder {
    override fun buildRoute(
        navigateId: String,
        defaultParamKeys: Set<String>?,
        listParam: List<NotiParam>?
    ): NotiRoute? {
        return null
    }
}