<resources xmlns:tools="http://schemas.android.com/tools">

    <!-- Allows us to override night specific attributes in the
         values-night folder. -->
    <style name="NightAdjusted.Theme.iBank" parent="android:Theme.Material.Light.NoActionBar" />

    <!-- The final theme we use -->
    <style name="Theme.iBank" parent="NightAdjusted.Theme.iBank" />

    <style name="NightAdjusted.Theme.Splash" parent="Theme.SplashScreen">
        <item name="android:windowLightStatusBar" tools:targetApi="23">true</item>
        <item name="android:windowLightNavigationBar" tools:targetApi="27">true</item>
    </style>

    <style name="Theme.iBank.Splash" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="windowSplashScreenAnimatedIcon">@mipmap/ic_launcher</item>
        <item name="windowSplashScreenBackground">@android:color/white</item>
        <item name="postSplashScreenTheme">@style/Theme.iBank</item>
    </style>

</resources>