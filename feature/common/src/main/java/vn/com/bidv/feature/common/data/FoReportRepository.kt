package vn.com.bidv.feature.common.data

import vn.com.bidv.feature.common.data.foreport.apis.FoReportApi
import vn.com.bidv.feature.common.data.foreport.model.DataListTxnCountResponseDto
import vn.com.bidv.feature.common.data.foreport.model.DataListTxnFundTransferDto
import vn.com.bidv.feature.common.data.foreport.model.DataListTxnLogDto
import vn.com.bidv.feature.common.data.foreport.model.ExportDocumentResponseDto
import vn.com.bidv.feature.common.data.foreport.model.FundTransferBaseCriteriaRequestDto
import vn.com.bidv.feature.common.data.foreport.model.TxnLogCriteriaDto
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.network.domain.BaseRepository
import javax.inject.Inject

class FoReportRepository @Inject constructor(
    private val service: FoReportApi
) : BaseRepository() {
    suspend fun inquiryTxnLogs(txnId: String, txnCode: String): NetworkResult<DataListTxnLogDto> =
        launch {
            val request = TxnLogCriteriaDto(txnCode = txnCode, txnId = txnId)
            service.inquiryTxnLogs(request)
        }

    suspend fun getTxnCountPendingApprovalChecker(): NetworkResult<DataListTxnCountResponseDto> =
        launch {
            service.getTxnCountPendingApprovalChecker()
        }

    suspend fun getTxnCount(): NetworkResult<DataListTxnCountResponseDto> =
        launch {
            service.getTxnCount()
        }

    suspend fun getReportTransferAllList(
        fundTransferMakerCriteriaRequestDto: FundTransferBaseCriteriaRequestDto
    ): NetworkResult<DataListTxnFundTransferDto> = launch {
        service.getFundTransferList(
            fundTransferMakerCriteriaRequestDto
        )
    }

    suspend fun exportReport(
        fundTransferMakerCriteriaRequestDto: FundTransferBaseCriteriaRequestDto
    ): NetworkResult<ExportDocumentResponseDto> = launch {
        service.exportFundTransferList(
            fundTransferMakerCriteriaRequestDto
        )
    }
} 
