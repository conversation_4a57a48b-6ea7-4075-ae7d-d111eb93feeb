package vn.com.bidv.feature.common.utils

import vn.com.bidv.common.sharePreference.IKey
import vn.com.bidv.feature.common.constants.Constants

enum class CommonDataKeys (
    override val key: String,
    override val defValue: String? = null
) : <PERSON><PERSON><PERSON> {
    USER_PROFILE(Constants.USER_PROFILE),
    FIRST_LOGIN_TIME(Constants.FIRST_LOGIN_TIME);

    override val prefix: String = "COMMON"
}