package vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.scanqr

import android.Manifest
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.content.ContextCompat.startActivity
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.google.gson.Gson
import com.google.mlkit.vision.common.InputImage
import vn.com.bidv.common.extenstion.isNotNullOrEmpty
import vn.com.bidv.common.ui.BaseMVIScreen
import vn.com.bidv.common.utils.CollectSideEffect
import vn.com.bidv.common.utils.unpackV2
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankTopAppBar
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.feature.common.constants.Constants
import vn.com.bidv.feature.common.data.VerifyOtpResult
import vn.com.bidv.feature.common.domain.data.QrCodeParseDMO
import vn.com.bidv.feature.common.domain.data.TransAuthDMO
import vn.com.bidv.feature.common.navigation.NavigationHelper
import vn.com.bidv.feature.common.navigation.VerifyTransactionRoute
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InitVerifyTransactionResponse
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionTypeConstant
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.scanqr.ScanQrReducer.ScanQrViewEvent
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.scanqr.ScanQrReducer.ScanQrViewState
import vn.com.bidv.feature.common.utils.ScanQrUtils
import vn.com.bidv.log.BLogUtil
import vn.com.bidv.sdkbase.utils.PermissionUtils
import vn.com.bidv.designsystem.R as RDesignSystem
import vn.com.bidv.feature.common.navigation.NavigationHelper as CommonNavigationHelper
import vn.com.bidv.localization.R as RLocalization


data class DataPopupError(
    val message: String,
    val buttonTitle: String = "",
    val modalConfirmType: ModalConfirmType,
    val handleAction: () -> Unit = {},
    val handleDismiss: () -> Unit
)

@Composable
fun ScanQrScreen(navController: NavHostController) {
    val vm: ScanQrViewModel = hiltViewModel()
    val (_, _onEvent, _) = vm.unpackV2()

    var dataPopupError by remember { mutableStateOf<DataPopupError?>(null) }


    PermissionUtils.checkPermissionAndDoSomeThing(
        navController.context,
        Manifest.permission.CAMERA,
        onPermissionGranted = {

        },
        onPermissionDenied = {
            dataPopupError = DataPopupError(
                message = navController.context.getString(RLocalization.string.vui_long_cap_quyen_cho_ung_dung_truy_cap_camera_cua_thiet_bi_de_quet_qr),
                buttonTitle = navController.context.getString(RLocalization.string.dong_y),
                modalConfirmType = ModalConfirmType.Info,
                handleAction = {
                    val intent = Intent(
                        Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                        Uri.fromParts("package", navController.context.packageName, null)
                    )
                    startActivity(navController.context, intent, null)
                },
                handleDismiss = {
                    dataPopupError = null
                }
            )
        }
    )

    val lifecycleOwner = androidx.lifecycle.compose.LocalLifecycleOwner.current
    val cameraProviderFuture = remember {
        ProcessCameraProvider.getInstance(navController.context)
    }
    val previewView = PreviewView(navController.context)
    val preview = androidx.camera.core.Preview.Builder().build()
    val selector =
        CameraSelector.Builder().requireLensFacing(CameraSelector.LENS_FACING_BACK)
            .build()
    preview.surfaceProvider = previewView.surfaceProvider
    val imageAnalysis = ImageAnalysis.Builder().build()
    imageAnalysis.setAnalyzer(
        ContextCompat.getMainExecutor(navController.context),
        BarcodeAnalyzer(
            onSuccess = {
                _onEvent(ScanQrViewEvent.OnScanQrSuccess(it))
                cameraProviderFuture.get().unbindAll()
            },
            onFail = {
                BLogUtil.d("onFail $it")
            }
        )
    )

    CollectSideEffect(vm.subscribeShareData(Constants.VERIFY_OTP)) {
        val otpResult = Gson().fromJson(it.data, VerifyOtpResult::class.java)
        when (otpResult.errorCode) {
            Constants.POP_VERIFY_TRANSACTION -> {
                bindCameraUseCases(
                    cameraProvider = cameraProviderFuture.get(),
                    lifecycleOwner = lifecycleOwner,
                    preview = preview,
                    imageAnalysis = imageAnalysis,
                    selector = selector
                )
            }

            else -> {
                //nothing
            }
        }
    }

    CameraScreen(
        cameraProvider = cameraProviderFuture.get(),
        lifecycleOwner = lifecycleOwner,
        previewView = previewView,
        preview = preview,
        imageAnalysis = imageAnalysis,
        selector = selector
    )

    BaseMVIScreen(
        viewModel = vm,
        renderContent = { uiState, onEvent ->

            ScanQrContent(
                uiState = uiState,
                onEvent = onEvent,
                navController = navController,
                cameraProvider = cameraProviderFuture.get()
            )

            HandlePopupScanQr(
                dataPopupError = dataPopupError
            )

        },
        handleSideEffect = { sideEffect ->
            when (sideEffect) {
                is ScanQrReducer.ScanQrViewEffect.ParseQr -> {
                    // nothing
                }

                is ScanQrReducer.ScanQrViewEffect.ParseQrError -> {
                    dataPopupError = DataPopupError(
                        message = sideEffect.errorMessage,
                        modalConfirmType = ModalConfirmType.Error,
                        handleDismiss = {
                            dataPopupError = null
                            bindCameraUseCases(
                                cameraProvider = cameraProviderFuture.get(),
                                lifecycleOwner = lifecycleOwner,
                                preview = preview,
                                imageAnalysis = imageAnalysis,
                                selector = selector
                            )
                        }
                    )
                }

                is ScanQrReducer.ScanQrViewEffect.ScanQrError -> {
                    dataPopupError = DataPopupError(
                        message = navController.context.getString(RLocalization.string.qr_khong_hop_le_vui_long_kiem_tra_lai),
                        modalConfirmType = ModalConfirmType.Error,
                        handleDismiss = {
                            dataPopupError = null
                            bindCameraUseCases(
                                cameraProvider = cameraProviderFuture.get(),
                                lifecycleOwner = lifecycleOwner,
                                preview = preview,
                                imageAnalysis = imageAnalysis,
                                selector = selector
                            )
                        }
                    )
                }

                is ScanQrReducer.ScanQrViewEffect.ParseQrSuccess -> {
                    if (sideEffect.data.additionalInfo?.qrType == Constants.BID_TRANS_OTP && sideEffect.isLoginSuccess) {
                        dataPopupError = DataPopupError(
                            message = navController.context.getString(RLocalization.string.de_xac_thuc_smart_otp_cho_giao_dich_quy_khach_vui_long_dang_xuat_va_chon_tinh_nang_quet_qr_tai_man_hinh_dang_nhap),
                            buttonTitle = navController.context.getString(RLocalization.string.dang_xuat),
                            modalConfirmType = ModalConfirmType.Info,
                            handleAction = {
                                _onEvent(ScanQrViewEvent.OnLogout)
                            },
                            handleDismiss = {
                                dataPopupError = null
                                bindCameraUseCases(
                                    cameraProvider = cameraProviderFuture.get(),
                                    lifecycleOwner = lifecycleOwner,
                                    preview = preview,
                                    imageAnalysis = imageAnalysis,
                                    selector = selector
                                )
                            }
                        )
                    } else {
                        handleParseQrSuccess(
                            sideEffect.data,
                            sideEffect.isLoginSuccess,
                            navController,
                            _onEvent
                        )
                    }
                }

                is ScanQrReducer.ScanQrViewEffect.LogoutSuccess -> {
                    vn.com.bidv.sdkbase.navigation.NavigationHelper.navigationToLogin(navController)
                }

                is ScanQrReducer.ScanQrViewEffect.ShareDataCreateTransaction,
                is ScanQrReducer.ScanQrViewEffect.Logout,
                is ScanQrReducer.ScanQrViewEffect.InitScreen -> {
                    // nothing
                }
            }
        },
    )
}

@Composable
private fun HandlePopupScanQr(
    dataPopupError: DataPopupError?
) {
    if (dataPopupError != null) {
        val listButton = when (dataPopupError.modalConfirmType) {
            ModalConfirmType.Info -> {
                listOf(
                    DialogButtonInfo(
                        label = dataPopupError.buttonTitle,
                        onClick = {
                            dataPopupError.handleAction.invoke()
                        }
                    ),
                    DialogButtonInfo(
                        label = stringResource(RLocalization.string.dong),
                    )

                )
            }

            else -> {
                listOf(
                    DialogButtonInfo(
                        label = stringResource(RLocalization.string.dong),
                    )
                )
            }
        }


        IBankModalConfirm(
            modalConfirmType = dataPopupError.modalConfirmType,
            title = stringResource(RLocalization.string.thong_bao),
            supportingText = dataPopupError.message,
            listDialogButtonInfo = listButton,
            onDismissRequest = dataPopupError.handleDismiss
        )
    }

}

private fun handleParseQrSuccess(
    data: QrCodeParseDMO,
    isLoginSuccess: Boolean,
    navController: NavHostController,
    onEvent: (ScanQrViewEvent) -> Unit
) {
    // sau cần map navigateId để navigate đúng
    if (data.navigateId.isNotNullOrEmpty() && data.externalInfo.isNotNullOrEmpty()) {
        if (isLoginSuccess) {
            NavigationHelper.navigateToCreateTransactionScreen(
                navController = navController,
                transactionData = data.externalInfo?.replace("\n", "") ?: return
            )
        } else {
            onEvent(ScanQrViewEvent.OnShareDataCreateTransaction(data.externalInfo))
            navController.popBackStack()
        }
    } else {
        val initVerifyTransactionResponse = InitVerifyTransactionResponse(
            transAuth = TransAuthDMO(
                additionalInfo = data.additionalInfo,
                isSameDevice = null
            )
        )
        navController.popBackStack(VerifyTransactionRoute.ScanQRScreenRoute.route, true)
        CommonNavigationHelper.navigateToVerifyByTypeCreateTransaction(
            navController = navController,
            dataString = Gson().toJson(initVerifyTransactionResponse),
            type = VerifyTransactionTypeConstant.SCAN_QR
        )
    }
}

@Composable
private fun CameraScreen(
    cameraProvider: ProcessCameraProvider,
    lifecycleOwner: androidx.lifecycle.LifecycleOwner,
    previewView: PreviewView,
    preview: androidx.camera.core.Preview,
    imageAnalysis: ImageAnalysis,
    selector: CameraSelector,
) {
    AndroidView(
        modifier = Modifier.fillMaxSize(),
        factory = { _ ->
            bindCameraUseCases(
                cameraProvider = cameraProvider,
                lifecycleOwner = lifecycleOwner,
                preview = preview,
                imageAnalysis = imageAnalysis,
                selector = selector
            )
            previewView
        }
    )
}

private fun unbindCameraUseCases(cameraProvider: ProcessCameraProvider) {
    cameraProvider.unbindAll()
}

private fun bindCameraUseCases(
    cameraProvider: ProcessCameraProvider,
    lifecycleOwner: androidx.lifecycle.LifecycleOwner,
    preview: androidx.camera.core.Preview,
    imageAnalysis: ImageAnalysis,
    selector: CameraSelector,
) {
    unbindCameraUseCases(cameraProvider)
    cameraProvider.bindToLifecycle(
        lifecycleOwner,
        selector,
        preview,
        imageAnalysis
    )
}

@Composable
private fun ScanQrContent(
    uiState: ScanQrViewState,
    onEvent: (ScanQrViewEvent) -> Unit,
    navController: NavHostController,
    cameraProvider: ProcessCameraProvider,
) {

    val launcher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let {
            val inputImage = InputImage.fromFilePath(navController.context, uri)
            ScanQrUtils.scanQrFromImage(
                inputImage,
                onFail = {
                    onEvent(ScanQrViewEvent.OnScanQrError(it))
                    cameraProvider.unbindAll()
                }, onSuccess = {
                    onEvent(ScanQrViewEvent.OnScanQrSuccess(it))
                    cameraProvider.unbindAll()
                }
            )
        }
    }

    ConstraintLayout(
        modifier = Modifier
            .fillMaxSize()
    ) {

        ScanQrFrame()
        val (columnTop, imageScanQr, columnBottom) = createRefs()

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .constrainAs(columnTop) {
                    top.linkTo(parent.top)
                }
        ) {
            IBankTopAppBar(
                navController = navController,
                topAppBarType = TopAppBarType.Title,
                topAppBarConfig = TopAppBarConfig(
                    isShowTopAppBar = false,
                    isShowNavigationIcon = true,
                    navigationIconColor = LocalColorScheme.current.contentOn_specialPrimary,
                    titleTopAppBar = stringResource(RLocalization.string.quet_ma_qr),
                    titleTopAppBarColor = LocalColorScheme.current.contentOn_specialPrimary,
                    showHomeIcon = uiState.isLoginSuccess
                )
            )

            Spacer(modifier = Modifier.size(IBSpacing.spacing3xl))

            Icon(
                painter = painterResource(RDesignSystem.drawable.bidv_logo),
                modifier = Modifier
                    .defaultMinSize(188.dp, 36.dp)
                    .align(Alignment.CenterHorizontally),
                tint = Color.Unspecified,
                contentDescription = null,
            )

            Spacer(modifier = Modifier.size(IBSpacing.spacing2xl))

            Row(modifier = Modifier
                .height(IntrinsicSize.Min)
                .fillMaxWidth(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    painter = painterResource(RDesignSystem.drawable.logo_bidv_full),
                    contentDescription = null,
                    tint = Color.Unspecified,
                )

                VerticalDivider(
                    color = LocalColorScheme.current.borderMainTertiary.copy(alpha = 0.2f),
                    thickness = 1.dp,
                    modifier = Modifier
                        .height(IBSpacing.spacingS)
                        .padding(horizontal = IBSpacing.spacingS)
                )

                Icon(
                    painter = painterResource(RDesignSystem.drawable.viet_qr),
                    contentDescription = null,
                    tint = Color.Unspecified,
                )

            }

        }

        val configuration = LocalConfiguration.current
        val screenWidth = (configuration.screenWidthDp * 0.8).dp

        Image(
            painter = painterResource(RDesignSystem.drawable.ic_border_scan_qr),
            contentDescription = null,
            modifier = Modifier
                .size(screenWidth)
                .constrainAs(imageScanQr) {
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
        )

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .constrainAs(columnBottom) {
                    top.linkTo(imageScanQr.bottom)
                }
                .padding(horizontal = 44.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.size(IBSpacing.spacing2xl))
            Text(
                text = stringResource(RLocalization.string.di_chuyen_camera_den_ma_qr_de_quet_hoac),
                color = LocalColorScheme.current.contentOn_specialPrimary,
                style = LocalTypography.current.titleTitle_s
            )

            Spacer(modifier = Modifier.size(IBSpacing.spacingM))

            Row(
                modifier = Modifier
                    .wrapContentWidth()
                    .border(
                        width = 1.dp,
                        color = LocalColorScheme.current.borderMainPrimary,
                        shape = RoundedCornerShape(8.dp)
                    )
                    .clip(shape = RoundedCornerShape(8.dp))
                    .padding(vertical = IBSpacing.spacingXs, horizontal = IBSpacing.spacingM)
                    .clickable(
                        onClick = { launcher.launch("image/*") },
                        indication = null,
                        interactionSource = remember { MutableInteractionSource() }),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    modifier = Modifier.size(IBSpacing.spacingM),
                    painter = painterResource(RDesignSystem.drawable.upload),
                    contentDescription = null,
                    tint = LocalColorScheme.current.contentOn_specialPrimary
                )

                Spacer(modifier = Modifier.size(IBSpacing.spacingXs))

                Text(
                    modifier = Modifier.wrapContentWidth(),
                    text = stringResource(RLocalization.string.tai_anh_len),
                    style = LocalTypography.current.labelLabel_l,
                    color = LocalColorScheme.current.contentOn_specialPrimary
                )
            }
        }
    }
}
