package vn.com.bidv.feature.common.data

import vn.com.bidv.feature.common.data.utilities.apis.UtilitiesApi
import vn.com.bidv.feature.common.data.utilities.model.OTTRegisterDeviceRequest
import vn.com.bidv.feature.common.data.utilities.model.SmartOtpDeletedRequest
import vn.com.bidv.feature.common.data.utilities.model.SmartOtpLockRequest
import vn.com.bidv.feature.common.data.utilities.model.TransParseQRRequest
import vn.com.bidv.feature.common.data.utilities.model.TransPushOtpRequest
import vn.com.bidv.feature.common.data.utilities.model.TransRetrieveOtpRequest
import vn.com.bidv.feature.common.data.utilities.model.UserDefaultAcctUpdateRequest
import vn.com.bidv.network.NetworkConfig
import vn.com.bidv.network.domain.BaseRepository
import javax.inject.Inject

class UtilitiesRepository @Inject constructor(
    private val utilitiesApi: UtilitiesApi,
    private val networkConfig: NetworkConfig
) : BaseRepository() {
    suspend fun lock(userId: String, smToken: String) {
        launch {
            utilitiesApi.lock(
                SmartOtpLockRequest(
                    userId = userId.toLongOrNull() ?: 0,
                    deviceId = networkConfig.deviceId,
                    smToken = smToken
                )
            )
        }
    }

    suspend fun selfLock() {
        launch {
            utilitiesApi.selfLock()
        }
    }

    suspend fun retrieveOtp(authId: String) = launch {
        utilitiesApi.retrieveOtp(TransRetrieveOtpRequest(authId = authId))
    }

    suspend fun parseQr(data: String) = launch {
        utilitiesApi.parseQR(TransParseQRRequest(data = data))
    }

    suspend fun pushOtp(authId: String, otp: String, smToken: String) = launch {
        utilitiesApi.pushOtp(
            TransPushOtpRequest(
                authId = authId,
                otp = otp,
                smtoken = smToken,
                deviceId = ""
            )
        )
    }

    suspend fun setDefaultAccount(accNo: String, accType: String, grpType: String, enable : Boolean) = launch {
        val request = UserDefaultAcctUpdateRequest(accNo, accType, grpType, enable)
        utilitiesApi.updateDefaultAccount(request)
    }

    suspend fun registerFCMId(fcmId: String) = launch {
        val request = OTTRegisterDeviceRequest(
            deviceModel = networkConfig.deviceModel,
            osVersion = networkConfig.osVersion,
            os = "ANDROID",
            deviceId = networkConfig.deviceId,
            lang = networkConfig.language,
            fcmId = fcmId
        )
        utilitiesApi.registerDevice(request)
    }

    suspend fun deleteUserSmartOtp(userId: Long) = launch {
        utilitiesApi.delete(smartOtpDeletedRequest = SmartOtpDeletedRequest(userId = userId))
    }
}

