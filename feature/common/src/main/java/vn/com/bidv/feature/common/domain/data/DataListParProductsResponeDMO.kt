package vn.com.bidv.feature.common.domain.data

import com.google.gson.annotations.SerializedName

data class DataListParProductsResponseDMO(
    @SerializedName("items")
    val items: List<ParProductsResponseDMO>? = null,
    @SerializedName("total")
    val total: Long? = null,
)

data class ParProductsResponseDMO(
    @SerializedName("code")
    val code: String? = null,
    @SerializedName("parentCode")
    val parentCode: String? = null,
    @SerializedName("name")
    val name: String? = null,
    @SerializedName("description")
    val description: String? = null,
)
