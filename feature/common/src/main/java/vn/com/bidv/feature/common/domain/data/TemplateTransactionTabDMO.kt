package vn.com.bidv.feature.common.domain.data

import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController

data class TemplateTransactionTabDMO(
    val titleResourceID: Int = 0,
    val routeID: TransactionTabRouteID? = null,
    val content: (@Composable (navController: NavHostController) -> Unit)? = null,
)

enum class TransactionTabRouteID {
    PaymentTab,
    CnrTab
}
