package vn.com.bidv.feature.common.domain.data

import vn.com.bidv.feature.common.constants.MenuCode
import vn.com.bidv.localization.R

data class MenuDMO(
    val isHeader: Boolean = false,
    val count: Int = 0,
    val code: String,
) {
    val route: TransactionRouteID get() = code.toTransactionRouteID()
}

enum class TransactionRouteID(
    val deeplinkRouteID: String = "",
    val titleRes: Int = 0,
) {
    PaymentReport(MenuCode.M_RPT_DMC(), R.string.chuyen_tien_trong_nuoc),
    PaymentBulkReport(MenuCode.M_RPT_BULK(), R.string.thanh_toan_bang_ke),
    PaymentSalaryReport(MenuCode.M_RPT_SLY(), R.string.thanh_toan_luong),
    CnrReport(MenuCode.M_RPT_BILL(), R.string.thanh_toan_hoa_don_va_nap_tien),
    ScheduleTransferReport(MenuCode.M_RPT_MNE(), R.string.quan_ly_lenh_chuyen_tien_dat_lich),
    TransferCheckReport(MenuCode.M_RPT_CHECK(), R.string.tra_soat),
    DepositReport(MenuCode.M_RPT_SEND(), R.string.gui_tien),
    WithdrawReport(MenuCode.M_RPT_WDW(), R.string.rut_tien),

    DepositApproval(MenuCode.M_APPROVE_DPT_SEND(), R.string.gui_tien),
    WithdrawApproval(MenuCode.M_APPROVE_DPT_WDW(), R.string.rut_tien),
    TransferPaymentApproval(MenuCode.M_APPROVE_PMT(), R.string.chuyen_tien_va_thanh_toan),
    PaymentApproval(MenuCode.M_APPROVE_PMT_DMC(), R.string.chuyen_tien_trong_nuoc),
    CnrApproval(MenuCode.M_APPROVE_PMT_BILL(), R.string.thanh_toan_hoa_don_va_nap_tien),
    TransferRecallApproval(MenuCode.M_APPROVE_PMT_RCL(), R.string.thu_hoi_giao_dich),
    DepositInvestApproval(MenuCode.M_APPROVE_DPT(), R.string.tien_gui_va_dau_tu),
    DepositRecallApproval(MenuCode.M_APPROVE_DPT_RCL(), R.string.thu_hoi_giao_dich_gui_tien),
    VerifyRequestApproval(MenuCode.M_APPROVE_RQT(), R.string.yeu_cau_tra_soat_va_gui_chung_tu),
    PaymentCheckApproval(MenuCode.M_APPROVE_RQT_CHECK(), R.string.tra_soat),
    PaymentBulkApproval(MenuCode.M_APPROVE_PMT_BULK(), R.string.thanh_toan_bang_ke),
    PaymentSalaryApproval(MenuCode.M_APPROVE_PMT_SLY(), R.string.thanh_toan_luong),

    LoanApproval(MenuCode.M_APPROVE_LOAN(), R.string.tin_dung_bao_lanh),
    DebtRepayment(MenuCode.M_APPROVE_LOAN_REPMT(), R.string.tra_no),
    LoanDisbursal(MenuCode.M_APPROVE_LOAN_DBMT(), R.string.giai_ngan),
    LoanGuarantee(MenuCode.M_APPROVE_LOAN_BG(), R.string.bao_lanh),
    LoanCreditCommit(MenuCode.M_APPROVE_LOAN_CREDITCMT(), R.string.cam_ket_tin_dung),

    PublicServiceApproval(MenuCode.M_APPROVE_M_PUBLICSVC(), R.string.dich_vu_cong),
    PublicServiceCusTaxApproval(MenuCode.M_APPROVE_PUBLICSVC_CUSTAX(), R.string.thue_phi_hai_quan),

    DEFAULT("", R.string.khong_co_du_lieu);

    var title: String = ""
        private set

    fun setTitle(title: String) {
        this.title = title
    }
}

private val routeIDMap = TransactionRouteID.entries.associateBy { it.deeplinkRouteID }
fun String.toTransactionRouteID(): TransactionRouteID = routeIDMap[this] ?: TransactionRouteID.DEFAULT