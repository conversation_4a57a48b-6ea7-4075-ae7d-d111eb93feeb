package vn.com.bidv.feature.homepage.ui.customquicklink.draggable

import android.graphics.Rect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.geometry.Offset
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlin.math.pow
import kotlin.math.sqrt

abstract class DraggableState<T>(
    private val coroutineScope: CoroutineScope,
    private val onSwap: (ItemInfo, ItemInfo) -> Unit,
    autoscroller: Autoscroller?,
    speedFactor: Float,
    isItemLocked: ((itemToSwap: ItemInfo) -> Boolean)?,
) {

    internal abstract val isGrid: Boolean
    internal abstract val isVertical: Boolean

    internal abstract val firstVisibleItemIndex: Int
    internal abstract val viewportStartOffset: Int
    internal abstract val viewportEndOffset: Int
    internal abstract val firstVisibleItemScrollOffset: Int
    internal abstract val layoutMode: LayoutMode

    internal abstract val T.itemIndex: Int
    internal abstract val T.itemKey: Any?
    internal abstract val T.top: Int
    internal abstract val T.left: Int
    internal abstract val T.right: Int
    internal abstract val T.bottom: Int
    internal abstract val T.width: Int
    internal abstract val T.height: Int
    internal abstract val visibleItemsInfo: List<T>

    internal abstract suspend fun scrollBy(distance: Float)
    internal abstract suspend fun scrollToItem(index: Int, scrollOffset: Int)

    private var _draggedDistance by mutableStateOf(Offset(0f, 0f))
    private val draggedDistance: Offset
        get() = _draggedDistance

    private var overscrollJob: Job? = null

    internal val cancelDragAnimation = CancelDragAnimation()
    private val autoScroller by lazy {
        autoscroller ?: DefaultAutoscroller(speedFactor)
    }

    private val itemMoveOperationResolver = ItemMoveOperationResolver<T>(
        isItemLocked = isItemLocked,
        layoutMode = { layoutMode },
        itemIndex = { item -> item.itemIndex },
        itemKey = { item -> item.itemKey },
        itemTop = { item -> item.top },
        itemLeft = { item -> item.left },
        itemRight = { item -> item.right },
        itemBottom = { item -> item.bottom },
        itemWidth = { item -> item.width },
        itemHeight = { item -> item.height },
        hoveredItem = { item ->
            hoveredItemKey = item?.itemKey
            hoveredItemIndex = item?.itemIndex
        }
    )

    private var currentlyDraggedItem by mutableStateOf<T?>(null)

    var draggingItemIndex by mutableStateOf<Int?>(null)
        private set

    internal val draggingItemKey: Any?
        get() = currentlyDraggedItem?.itemKey

    val draggingItemLeft: Float
        get() = draggingLayoutInfo?.let { item ->
            (currentlyDraggedItem?.left ?: 0) + draggedDistance.x - item.left
        } ?: 0f
    val draggingItemTop: Float
        get() = draggingLayoutInfo?.let { item ->
            (currentlyDraggedItem?.top ?: 0) + draggedDistance.y - item.top
        } ?: 0f
    private val draggingLayoutInfo: T?
        get() = visibleItemsInfo
            .firstOrNull { it.itemIndex == draggingItemIndex }

    internal var hoveredItemKey by mutableStateOf<Any?>(null)
    private var hoveredItemIndex by mutableStateOf<Int?>(null)

    internal fun onDragStart(key: Any) {
        visibleItemsInfo.find { item ->
            item.itemKey == key
        }?.let { item ->
            currentlyDraggedItem = item
            draggingItemIndex = item.itemIndex
        }
    }

    internal fun onDrag(dragAmount: Offset) {
        swapMovingItem(dragAmount = dragAmount)

        val overScroll = calculateOverscrollAmount()
        if (overScroll != 0f) {
            trackOverscroll()
        }
    }

    private fun swapMovingItem(
        dragAmount: Offset
    ) {
        currentlyDraggedItem ?: return
        _draggedDistance += dragAmount
        val draggingItemInfo = draggingLayoutInfo ?: return
        val startOffsetY = draggingItemInfo.top + draggingItemTop
        val startOffsetX = draggingItemInfo.left + draggingItemLeft

        val itemToSwap = itemMoveOperationResolver.findItemToSwap(draggingItemInfo, startOffsetX, startOffsetY, visibleItemsInfo) ?: return
        if (draggingItemInfo.itemIndex == itemToSwap.itemIndex) return

        if (itemToSwap.itemIndex == firstVisibleItemIndex || draggingItemInfo.itemIndex == firstVisibleItemIndex) {
            coroutineScope.launch {
                onSwap.invoke(
                    ItemInfo(draggingItemInfo.itemIndex, draggingItemInfo.itemKey),
                    ItemInfo(itemToSwap.itemIndex, itemToSwap.itemKey),
                )
                scrollToItem(firstVisibleItemIndex, firstVisibleItemScrollOffset)
            }
        } else {
            onSwap.invoke(
                ItemInfo(draggingItemInfo.itemIndex, draggingItemInfo.itemKey),
                ItemInfo(itemToSwap.itemIndex, itemToSwap.itemKey),
            )
        }

        draggingItemIndex = itemToSwap.itemIndex
    }

    private fun trackOverscroll() {
        val overScroll = calculateOverscrollAmount()
        if (overScroll != 0f && overscrollJob?.isActive != true) {
            overscrollJob = coroutineScope.launch {
                autoscroll(
                    when {
                        overScroll > 0f -> DragDirection.END
                        overScroll < -0f -> DragDirection.START
                        else -> DragDirection.NONE
                    }
                )
            }
        }
    }

    private suspend fun autoscroll(dragDirection: DragDirection = DragDirection.NONE) {
        while (currentlyDraggedItem != null && calculateOverscrollAmount() != 0f) {
            autoScroller.start(
                dragDirection, calculateOverscrollAmount()
            ) { overScroll ->
                scrollBy(overScroll)
            }

            swapMovingItem(Offset(0f, 0f))
        }
    }

    private fun calculateOverscrollAmount(): Float {
        val currentDragItem = draggingLayoutInfo ?: return 0f
        val initialOffset: Float
        val finalOffset: Float
        val scrollDelta: Float

        if (isVertical) {
            initialOffset = currentDragItem.top + draggingItemTop
            finalOffset = initialOffset + currentDragItem.height
            scrollDelta = draggedDistance.y
        } else {
            initialOffset = currentDragItem.left + draggingItemLeft
            finalOffset = initialOffset + currentDragItem.width
            scrollDelta = draggedDistance.x
        }

        return when {
            scrollDelta > 0 -> (finalOffset - viewportEndOffset).coerceAtLeast(0f)
            scrollDelta < 0 -> (initialOffset - viewportStartOffset).coerceAtMost(0f)
            else -> 0f
        }
    }

    fun onDragCanceled() {
        draggingItemIndex?.let { index ->
            val key = draggingItemKey
            val offset = Offset(draggingItemLeft, draggingItemTop)
            coroutineScope.launch {
                cancelDragAnimation.animatableChangePosition(
                    index,
                    key,
                    offset
                )
            }
        }
        currentlyDraggedItem = null
        draggingItemIndex = null
        hoveredItemKey = null
        hoveredItemIndex = null
        _draggedDistance = Offset(0f, 0f)
        cancelOverscrollJob()
    }

    private fun cancelOverscrollJob() {
        overscrollJob?.cancel()
        overscrollJob = null
    }
}


class ItemMoveOperationResolver<T>(
    private val isItemLocked: ((itemToSwap: ItemInfo) -> Boolean)?,
    private val layoutMode: () -> LayoutMode,
    private val itemIndex: (T) -> Int,
    private val itemKey: (T) -> Any?,
    private val itemTop: (T) -> Int,
    private val itemLeft: (T) -> Int,
    private val itemRight: (T) -> Int,
    private val itemBottom: (T) -> Int,
    private val itemWidth: (T) -> Int,
    private val itemHeight: (T) -> Int,
    private val hoveredItem: (T?) -> Unit,
) {

    companion object {
        const val SQUARED_EXPONENT = 2.0
    }

    fun findItemToSwap(
        currentDraggingItem: T,
        x: Float,
        y: Float,
        visibleItems: List<T>
    ): T? {
        val left = x
        val top = y
        val right = left + itemWidth(currentDraggingItem)
        val bottom = top + itemHeight(currentDraggingItem)
        val dragRect = Rect(left.toInt(), top.toInt(), right.toInt(), bottom.toInt())
        findHoveredItem(currentDraggingItem, visibleItems, dragRect)
        return getClosestItem(
            currentDraggingItem,
            findOverlappedItems(currentDraggingItem, left, top, dragRect, visibleItems),
        )
    }

    private fun findHoveredItem(
        currentDraggingItem: T,
        visibleItems: List<T>,
        dragRect: Rect,
    ) {
        val items = visibleItems
            .filter { item ->
                if (itemIndex(item) == itemIndex(currentDraggingItem)
                    || itemKey(item) == itemKey(currentDraggingItem)
                    || isItemLocked(item)
                ) {
                    return@filter false
                }

                dragRect.overlaps(
                    Rect(
                        itemLeft(item),
                        itemTop(item),
                        itemRight(item),
                        itemBottom(item)
                    ),
                    coveredArea = 0.25
                )
            }


        hoveredItem(
            getClosestItem(
                currentDraggingItem,
                items,
            )
        )
    }

    private fun findOverlappedItems(
        currentDraggingItem: T,
        draggingItemLeft: Float,
        draggingItemTop: Float,
        draggingItemRect: Rect,
        visibleItems: List<T>
    ): List<T> {
        return visibleItems
            .filter { item ->
                if (itemIndex(item) == itemIndex(currentDraggingItem)
                    || itemKey(item) == itemKey(currentDraggingItem)
                    || isItemLocked(item)
                ) {
                    return@filter false
                }

                draggingItemRect.overlaps(
                    Rect(
                        itemLeft(item),
                        itemTop(item),
                        itemRight(item),
                        itemBottom(item)
                    )
                )
            }
            .filter { item ->
                val deltaX = (draggingItemLeft - itemLeft(currentDraggingItem))
                val deltaY = (draggingItemTop - itemTop(currentDraggingItem))

                when (layoutMode()) {
                    LayoutMode.GRID -> {
                        when {
                            deltaX > 0 && deltaY > 0 -> {
                                itemLeft(item) < draggingItemLeft && itemTop(item) < draggingItemTop
                            }

                            deltaX < 0 && deltaY < 0 -> {
                                itemLeft(item) > draggingItemLeft && itemTop(item) > draggingItemTop
                            }

                            deltaX > 0 && deltaY < 0 -> {
                                itemLeft(item) < draggingItemLeft && itemTop(item) > draggingItemTop
                            }

                            deltaX < 0 && deltaY > 0 -> {
                                itemLeft(item) > draggingItemLeft && itemTop(item) < draggingItemTop
                            }

                            else -> false
                        }
                    }

                    LayoutMode.VERTICAL_LIST -> {
                        if (deltaY > 0) itemTop(item) < draggingItemTop else itemTop(item) > draggingItemTop
                    }

                    LayoutMode.HORIZONTAL_LIST -> {
                        if (deltaX > 0) itemLeft(item) < draggingItemLeft else itemLeft(item) > draggingItemLeft
                    }
                }
            }
    }

    private fun getClosestItem(
        currentDraggingItem: T,
        overlappingItems: List<T>
    ): T? {
        return overlappingItems.minByOrNull { item ->
            getCenterDistance(currentDraggingItem, item)
        }
    }

    private fun getCenterDistance(item1: T, item2: T): Double {
        val centerX1 = (itemLeft(item1) + itemRight(item1)) / SQUARED_EXPONENT
        val centerY1 = (itemTop(item1) + itemBottom(item1)) / SQUARED_EXPONENT

        val centerX2 = (itemLeft(item2) + itemRight(item2)) / SQUARED_EXPONENT
        val centerY2 = (itemTop(item2) + itemBottom(item2)) / SQUARED_EXPONENT

        //Calculate distance using Euclidean Distance formula
        return sqrt(
            (centerX2 - centerX1).pow(SQUARED_EXPONENT) + (centerY2 - centerY1).pow(SQUARED_EXPONENT)
        )
    }

    private fun Rect.overlaps(other: Rect, coveredArea: Double = 0.01): Boolean {
        return when (layoutMode()) {
            LayoutMode.GRID ->
                left + (other.width() * coveredArea) < other.right &&
                        right > other.left + (other.width() * coveredArea) &&
                        top + (other.height() * coveredArea) < other.bottom &&
                        bottom > other.top + (other.height() * coveredArea)

            LayoutMode.VERTICAL_LIST ->
                top + (other.height() * coveredArea) < other.bottom
                        && bottom > other.top + (other.height() * coveredArea)

            LayoutMode.HORIZONTAL_LIST ->
                left + (other.width() * coveredArea) < other.right
                        && right > other.left + (other.width() * coveredArea)
        }
    }

    private fun isItemLocked(item: T): Boolean {
        return isItemLocked?.invoke(
            ItemInfo(
                index = itemIndex(item),
                key = itemKey(item),
            )
        ) ?: false
    }
}

enum class LayoutMode {
    GRID, VERTICAL_LIST, HORIZONTAL_LIST
}


enum class DragDirection {
    START, END, NONE
}

data class ItemInfo(
    val index: Int,
    val key: Any?,
)