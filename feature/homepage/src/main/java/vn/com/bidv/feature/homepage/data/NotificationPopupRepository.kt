package vn.com.bidv.feature.homepage.data

import vn.com.bidv.feature.common.data.utilitiesnotify.apis.UtilitiesNotifyStatementsApi
import vn.com.bidv.feature.common.data.utilitiesnotify.model.DataListPopupNotifyStatementResponse
import vn.com.bidv.feature.common.data.utilitiesnotify.model.PopupNotifyUserStatementRequest
import vn.com.bidv.feature.common.data.utilitiesnotify.model.ResultString
import vn.com.bidv.feature.homepage.domain.notificationpopupdmo.ModelPopupNotifyUserStatementRequestDMO
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.network.domain.BaseRepository
import javax.inject.Inject

class NotificationPopupRepository @Inject constructor(
    private val service: UtilitiesNotifyStatementsApi
) : BaseRepository() {

    suspend fun getPopupUser(): NetworkResult<DataListPopupNotifyStatementResponse> = launch {
        service.getPopupUser()
    }

    suspend fun updateStatusPopupUser(
        popupNotifyUserStatementRequestDMO: ModelPopupNotifyUserStatementRequestDMO
    ): NetworkResult<ResultString> = launch {
        service.updateStatusPopupUser(
            PopupNotifyUserStatementRequest(
                id = popupNotifyUserStatementRequestDMO.id,
                type = popupNotifyUserStatementRequestDMO.type,
            )
        )
    }

}