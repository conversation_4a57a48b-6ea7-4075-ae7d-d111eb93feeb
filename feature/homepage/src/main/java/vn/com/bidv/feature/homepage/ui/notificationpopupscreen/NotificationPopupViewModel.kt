package vn.com.bidv.feature.homepage.ui.notificationpopupscreen

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.feature.homepage.constants.NotificationPopupConstants.STATUS_UPDATE_FEEDBACK
import vn.com.bidv.feature.homepage.domain.NotificationPopupUseCase
import vn.com.bidv.feature.homepage.ui.notificationpopupscreen.NotificationPopupReducer.NotificationPopupViewEffect
import vn.com.bidv.feature.homepage.ui.notificationpopupscreen.NotificationPopupReducer.NotificationPopupViewEvent
import vn.com.bidv.feature.homepage.ui.notificationpopupscreen.NotificationPopupReducer.NotificationPopupViewState
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class NotificationPopupViewModel @Inject constructor(
    private val notificationPopupUseCase: NotificationPopupUseCase
) : ViewModelIBankBase<
        NotificationPopupViewState,
        NotificationPopupViewEvent,
        NotificationPopupViewEffect>(
    initialState = NotificationPopupViewState.Result(),
    reducer = NotificationPopupReducer()
) {
    override fun handleEffect(
        sideEffect: NotificationPopupViewEffect,
        onResult: (NotificationPopupViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is NotificationPopupViewEffect.GetNotificationPopup -> callDomain(
                showLoadingIndicator = false,
                isListenAllError = true,
                onFail = {
                    onResult(
                        NotificationPopupViewEvent.GetNotificationPopupFail
                    )
                },
                onSuccess = {
                    onResult(
                        NotificationPopupViewEvent.GetNotificationPopupSuccess(
                            listNotificationPopupResponseDMO = it.data
                        )
                    )
                }
            ) {
                notificationPopupUseCase.getNotificationPopup(isFromStorage = sideEffect.isFromStorage)
            }

            is NotificationPopupViewEffect.UpdateStatusDisplayPopup -> callDomain(
                showLoadingIndicator = false,
                isListenAllError = true,
                onFail = {
                    // do nothing
                },
            ) {
                notificationPopupUseCase.updateStatusNotificationPopup(
                    id = sideEffect.id,
                    type = sideEffect.type
                )
            }

            is NotificationPopupViewEffect.UpdateStatusFeedbackPopup -> callDomain(
                isListenAllError = true,
                onFail = {
                    onResult(
                        NotificationPopupViewEvent.UpdateStatusFeedbackPopupFail(
                            errorMessage = it?.errorMessage
                        )
                    )
                },
                onSuccess = {
                    onResult(
                        NotificationPopupViewEvent.UpdateStatusFeedbackPopupSuccess
                    )
                }
            ) {
                notificationPopupUseCase.updateStatusNotificationPopup(
                    id = sideEffect.id,
                    type = STATUS_UPDATE_FEEDBACK
                )
            }

            is NotificationPopupViewEffect.RemovePopupInCacheById -> {
                notificationPopupUseCase.removePopupInCacheById(id = sideEffect.id)
            }

            else -> {
                // do nothing
            }
        }
    }

    fun isPopupLoadedFirst() = localRepository.isPopupLoaded()
}
