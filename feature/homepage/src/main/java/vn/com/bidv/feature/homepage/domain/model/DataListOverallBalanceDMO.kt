package vn.com.bidv.feature.homepage.domain.model

import com.google.gson.annotations.SerializedName


data class DataListOverallBalanceDMO (

    @SerializedName("items")
    val items: List<OverallBalanceDMO>? = null,

    @SerializedName("total")
    val total: Long? = null

)


data class OverallBalanceDMO (

    @SerializedName("type")
    val type: String? = null,

    @SerializedName("totalBalance")
    val totalBalance: java.math.BigDecimal? = null,

    @SerializedName("balAccTypeList")
    val balAccTypeList: List<BalanceAccTypeDMO>? = null,

    @SerializedName("updatedDate")
    val updatedDate: String? = null
)

data class BalanceAccTypeDMO (

    @SerializedName("accType")
    val accType: String? = null,

    @SerializedName("totalBalance")
    val totalBalance: java.math.BigDecimal? = null

)