package vn.com.bidv.feature.homepage.ui.setting

import IBGradient
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.feedback.bottomsheet.IBankBottomSheet
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.homepage.navigation.HomePageNavigationHelper
import vn.com.bidv.feature.homepage.ui.homepage.MockNavigate
import vn.com.bidv.sdkbase.ui.EmptyViewModel
import vn.com.bidv.designsystem.R as RDesignSystem
import vn.com.bidv.localization.R as RLocalization

@Composable
fun SettingScreen(navController: NavHostController) {
    val vm: EmptyViewModel = hiltViewModel()
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current
    BaseScreen(
        navController = navController,
        viewModel = vm,
        renderContent = { _, _ ->
            Column {
                Box(
                    modifier = Modifier
                        .wrapContentHeight()
                        .fillMaxWidth()
                        .background(IBGradient.bg_topnav)
                        .windowInsetsPadding(
                            WindowInsets.statusBars
                        )
                ) {
                    Text(
                        text = stringResource(RLocalization.string.cai_dat),
                        color = colorScheme.contentOn_specialPrimary,
                        style = typography.titleTitle_m,
                        textAlign = TextAlign.Center,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = IBSpacing.spacing2xl, bottom = IBSpacing.spacingM),
                    )
                }
                SettingContent(navController = navController)
            }
        },
        handleSideEffect = {

        },
        isLightStatusBar = false,
        backgroundColor = colorScheme.bgMainPrimary,
        topAppBarType = TopAppBarType.Title,
        topAppBarConfig = TopAppBarConfig(
            isShowTopAppBar = false
        )
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SettingContent(
    navController: NavHostController) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .padding(IBSpacing.spacingM)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingM),
        ) {

            SettingNavigationCard(
                iconId = RDesignSystem.drawable.thong_tin_nguoi_dung_outline,
                title = stringResource(RLocalization.string.thong_tin_nguoi_dung),
                onClick = {
                    HomePageNavigationHelper.navigateToUserInfoScreen(navController)
                }
            )

            SettingNavigationCard(
                iconId = RDesignSystem.drawable.cai_dat_giao_dien_outline,
                title = stringResource(RLocalization.string.cai_dat_hinh_dai_dien),
                onClick = {
                    HomePageNavigationHelper.navigateToAvatarPickerScreen(navController)
                }
            )

            SettingNavigationCard(
                iconId = RDesignSystem.drawable.cai_dat_thong_tin_bao_mat_outline,
                title = stringResource(RLocalization.string.cai_dat_thong_tin_bao_mat),
                onClick = {
                    HomePageNavigationHelper.navigateToSettingSecureInfoScreen(navController)
                }
            )

            SettingNavigationCard(
                iconId = RDesignSystem.drawable.cai_dat_thong_bao_outline,
                title = stringResource(RLocalization.string.cai_dat_thong_bao),
                onClick = {
                    HomePageNavigationHelper.navigateToNotificationSettingScreen(navController)
                }
            )

            SettingNavigationCard(
                iconId = RDesignSystem.drawable.cai_dat_ngon_ngu_outline,
                title = stringResource(RLocalization.string.cai_dat_ngon_ngu),
                onClick = {
                    HomePageNavigationHelper.navigateToSettingLanguageScreen(navController)
                }
            )

            var isShowBottomSheet by remember { mutableStateOf(false) }
            SettingNavigationCard(
                iconId = RDesignSystem.drawable.interface_setting_menu,
                title = "Menu_Route_Test",
                onClick = {
                    isShowBottomSheet = true
                }
            )

            if (isShowBottomSheet) {
                IBankBottomSheet(
                    title = "Menu_Route_Test",
                    onDismiss = {
                        isShowBottomSheet = false
                    }
                ) {
                    MockNavigate(navController)
                }
            }
        }
    }
}

@Composable
private fun SettingNavigationCard(
    iconId: Int,
    title: String,
    onClick: () -> Unit
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current

    Card(
        shape = RoundedCornerShape(IBSpacing.spacingS),
        colors = CardDefaults.cardColors(containerColor = colorScheme.bgMainTertiary),
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable(
                    interactionSource = remember { MutableInteractionSource() },
                    indication = ripple(
                        bounded = true,
                        color = colorScheme.bgSolidPrimary_press,
                    ),
                    onClick = onClick
                )
                .heightIn(min = 72.dp)
                .padding(
                    horizontal = IBSpacing.spacingM,
                    vertical = IBSpacing.spacingXs
                ),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .size(IBSpacing.spacing4xl)
                    .background(
                        color = colorScheme.bgMainPrimary,
                        shape = CircleShape
                    )
                    .padding(IBSpacing.spacingXs),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = ImageVector.vectorResource(iconId),
                    contentDescription = null,
                    tint = colorScheme.contentMainPrimary,
                    modifier = Modifier.size(IBSpacing.spacing2xl)
                )
            }

            Spacer(modifier = Modifier.width(IBSpacing.spacingM))

            Box(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    style = typography.bodyBody_l,
                    color = colorScheme.contentMainPrimary,
                    modifier = Modifier.height(IBSpacing.spacing2xl)
                )
            }

            Icon(
                imageVector = ImageVector.vectorResource(RDesignSystem.drawable.arrow_right_outline),
                contentDescription = null,
                tint = colorScheme.contentMainPrimary,
                modifier = Modifier.size(IBSpacing.spacingL)
            )
        }
    }
}