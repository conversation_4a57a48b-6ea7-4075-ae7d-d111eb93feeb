package vn.com.bidv.feature.homepage.constants

import androidx.annotation.StringRes
import vn.com.bidv.localization.R as RLocalization

enum class BiometricError(@StringRes val resId: Int) {
    DEVICE_NOT_SUPPORT_BIOMETRIC(RLocalization.string.thiet_bi_khong_ho_tro_xac_thuc_touch_id_vui_long_kiem_tra_lai),
    FACE_TOUCH_ID_01(RLocalization.string.quy_khach_chua_cai_dat_touch_id_tai_thiet_bi_vui_long_kiem_tra_lai);

    companion object {
        fun getBiometricError(name: String?): BiometricError? {
            return entries.find { it.name == name }
        }
    }
}