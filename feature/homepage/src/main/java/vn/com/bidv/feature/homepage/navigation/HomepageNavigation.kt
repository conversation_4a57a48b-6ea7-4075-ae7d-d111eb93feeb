package vn.com.bidv.feature.homepage.navigation

import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.compose.composable
import androidx.navigation.navigation
import androidx.navigation.toRoute
import kotlinx.serialization.Serializable
import vn.com.bidv.feature.common.domain.data.PermissionResDMO
import vn.com.bidv.feature.homepage.navigation.HomepageRoute.AVATAR_PICKER_ROUTE
import vn.com.bidv.feature.homepage.ui.accountinquiry.AccountInquiryScreen
import vn.com.bidv.feature.homepage.ui.advertisementbanner.model.AdvertisementBannerModelUI
import vn.com.bidv.feature.homepage.ui.avatarpicker.AvatarPickerScreen
import vn.com.bidv.feature.homepage.ui.contact.ContactScreen
import vn.com.bidv.feature.homepage.ui.customquicklink.CustomQuickLinkScreen
import vn.com.bidv.feature.homepage.ui.customwidget.CustomWidgetScreen
import vn.com.bidv.feature.homepage.ui.homepage.HomePageScreen
import vn.com.bidv.feature.homepage.ui.listService.ListServiceScreen
import vn.com.bidv.feature.homepage.ui.setting.SettingScreen
import vn.com.bidv.feature.homepage.ui.settingsecureinfo.SettingSecureInfoScreen
import vn.com.bidv.sdkbase.component.screen.IBankWebviewBaseScreen
import vn.com.bidv.sdkbase.navigation.FeatureGraphBuilder
import vn.com.bidv.sdkbase.navigation.IBankMainRouting
import vn.com.bidv.sdkbase.utils.CustomSafeArgs
import javax.inject.Inject
import kotlin.reflect.typeOf

internal object HomepageRoute {
    const val HOME_PAGE_ROUTE = "HomePageScreen"
    const val USER_SETTING_ROUTE = "UserSettingScreen"
    const val SETTING_SECURE_INFO_ROUTE = "SettingSecureInfoRoute"
    const val CUSTOM_QUICK_LINK_ROUTE = "CustomQuickLinkRoute"
    const val CUSTOM_WIDGET_ROUTE = "CustomWidgetRoute"
    const val LIST_SERVICE_ROUTE = "ListServiceScreen"
    const val AVATAR_PICKER_ROUTE = "AvatarPickerScreen"
    const val ACCOUNT_INQUIRY_ROUTE = "AccountInquiryScreen"
    const val CONTACT_SCREEN = "contactScreen"

    @Serializable
    data class ListServiceRoute(val permissionResDMO: PermissionResDMO)

    @Serializable
    data class WebViewBannerRoute(val advertisementBannerModelUI: AdvertisementBannerModelUI)
}

class HomepageNavigation @Inject constructor() : FeatureGraphBuilder {

    override fun buildGraph(
        navGraphBuilder: NavGraphBuilder,
        navController: NavHostController,
        registeredRoutes: (args: List<String>) -> Unit
    ) {
        navGraphBuilder.navigation(
            startDestination = HomepageRoute.HOME_PAGE_ROUTE,
            route = IBankMainRouting.HomeRoute.HomeMainRoute.route
        ) {
            composable(
                route = HomepageRoute.HOME_PAGE_ROUTE
            ) {
                HomePageScreen(navController = navController)
            }
            composable<HomepageRoute.ListServiceRoute>(
                typeMap = mapOf(
                    typeOf<PermissionResDMO>() to CustomSafeArgs.SerializableNavType(
                        PermissionResDMO.serializer()
                    )
                )
            ) {
                val arguments = it.toRoute<HomepageRoute.ListServiceRoute>()
                ListServiceScreen(
                    navController,
                    arguments.permissionResDMO
                )
            }

            composable(
                route = HomepageRoute.CUSTOM_QUICK_LINK_ROUTE
            ) {
                CustomQuickLinkScreen(
                    navController
                )
            }

            composable(
                route = HomepageRoute.USER_SETTING_ROUTE
            ) {
                SettingScreen(navController)
            }

            composable(
                route = HomepageRoute.SETTING_SECURE_INFO_ROUTE
            ) {
                SettingSecureInfoScreen(navController)
            }

            composable(
                route = HomepageRoute.CUSTOM_WIDGET_ROUTE
            ) {
                CustomWidgetScreen(
                    navController
                )
            }

            composable(
                route = HomepageRoute.ACCOUNT_INQUIRY_ROUTE
            ) {
               AccountInquiryScreen(navController, isTabHome = false)
            }

            composable<HomepageRoute.WebViewBannerRoute>(
                typeMap = mapOf(
                    typeOf<AdvertisementBannerModelUI>() to CustomSafeArgs.SerializableNavType(
                        AdvertisementBannerModelUI.serializer()
                    )
                )
            ) {
                val arguments = it.toRoute<HomepageRoute.WebViewBannerRoute>()
                IBankWebviewBaseScreen(
                    navController,
                    title = arguments.advertisementBannerModelUI.title,
                    url = arguments.advertisementBannerModelUI.url
                )
            }

            composable(
                route = HomepageRoute.CONTACT_SCREEN
            ) {
                ContactScreen(navController)
            }
        }

        navGraphBuilder.navigation(
            route = IBankMainRouting.HomeRoute.AvatarPickerRoute.route,
            startDestination = AVATAR_PICKER_ROUTE
        ) {
            composable(
                route = AVATAR_PICKER_ROUTE
            ) {
                AvatarPickerScreen(navController)
            }
        }

        registeredRoutes(
            listOf(
                IBankMainRouting.HomeRoute.HomeMainRoute.route,
                IBankMainRouting.HomeRoute.AvatarPickerRoute.route
            )
        )
    }
} 
