package vn.com.bidv.feature.homepage.ui.service.modelUI


import kotlinx.serialization.Serializable
import vn.com.bidv.designsystem.R
import vn.com.bidv.feature.common.constants.Constants
import vn.com.bidv.feature.common.constants.Constants.URL_TYPE
import vn.com.bidv.feature.common.domain.data.PermissionResDMO
import vn.com.bidv.feature.common.domain.data.TransactionRouteID
import vn.com.bidv.feature.common.navigation.CommonRoute
import vn.com.bidv.feature.homepage.navigation.HomepageRoute
import vn.com.bidv.sdkbase.navigation.CNRRoute
import vn.com.bidv.sdkbase.navigation.CNRRoute.Companion.STATUS_TRANSACTION
import vn.com.bidv.sdkbase.navigation.IBankMainRouting
import vn.com.bidv.sdkbase.navigation.IBankMainRouting.PaymentRecallRoute.Companion.PRODUCT_CODE
import vn.com.bidv.sdkbase.navigation.IBankMainRouting.RevokeTransactionRoute.Companion.TAB_ID
import vn.com.bidv.sdkbase.navigation.IBankMainRouting.TemplateTransactionRoute.Companion.ROUTE_ID
import vn.com.bidv.sdkbase.navigation.IBankMainRouting.TransferRoute.Companion.tabId
import vn.com.bidv.sdkbase.navigation.IBankMainRouting.UtilitiesRoute
import vn.com.bidv.sdkbase.ui.BaseUIModel
import vn.com.bidv.sdkbase.utils.TransactionStatusBase

enum class PermissionCode(val code: String, val iconRes: Int, val titleRes: Int,val titleGroupRes: Int, val routeID: String? = null) {
    M_HOME("M_HOME", R.drawable.home, vn.com.bidv.localization.R.string.trang_chu, 0),
    M_APPROVE("M_APPROVE", R.drawable.duyet_giao_dich, vn.com.bidv.localization.R.string.duyet_giao_dich, 0, IBankMainRouting.TransactionApprovalRoute.TransactionApprovalMainRoute.route),
    M_APPROVE_PMT("M_APPROVE_PMT", R.drawable.chuyen_tien_trong_nuoc, vn.com.bidv.localization.R.string.chuyen_tien_va_thanh_toan, 0),
    M_APPROVE_PMT_DMC("M_APPROVE_PMT_DMC", R.drawable.chuyen_tien_trong_nuoc, vn.com.bidv.localization.R.string.chuyen_tien_trong_nuoc, 0,IBankMainRouting.TransactionApprovalRoute.TransactionApprovalMainRoute.routeWithArgument.replace("{${ROUTE_ID}}", TransactionRouteID.PaymentApproval.deeplinkRouteID)),
    M_APPROVE_PMT_BULK("M_APPROVE_PMT_BULK", R.drawable.thanh_toan_bang_ke, vn.com.bidv.localization.R.string.thanh_toan_bang_ke, 0,  routeID = IBankMainRouting.TransactionApprovalRoute.TransactionApprovalMainRoute.routeWithArgument.replace("{${ROUTE_ID}}", TransactionRouteID.PaymentBulkApproval.deeplinkRouteID)),
    M_APPROVE_PMT_SLY("M_APPROVE_PMT_SLY", R.drawable.thanh_toan_luong, vn.com.bidv.localization.R.string.thanh_toan_luong, 0,  routeID = IBankMainRouting.TransactionApprovalRoute.TransactionApprovalMainRoute.routeWithArgument.replace("{${ROUTE_ID}}", TransactionRouteID.PaymentSalaryApproval.deeplinkRouteID)),
    M_APPROVE_PMT_BILL("M_APPROVE_PMT_BILL", R.drawable.thanh_toan_hoa_don, vn.com.bidv.localization.R.string.thanh_toan_hoa_don_va_nap_tien, 0, IBankMainRouting.TransactionApprovalRoute.TransactionApprovalMainRoute.routeWithArgument.replace("{${ROUTE_ID}}", TransactionRouteID.CnrApproval.deeplinkRouteID)),
    M_APPROVE_PMT_RCL("M_APPROVE_PMT_RCL", R.drawable.thu_hoi_giao_dich, vn.com.bidv.localization.R.string.thu_hoi_giao_dich, 0, routeID = IBankMainRouting.TransactionApprovalRoute.TransactionApprovalMainRoute.routeWithArgument.replace("{${ROUTE_ID}}", TransactionRouteID.TransferRecallApproval.deeplinkRouteID)),
    M_APPROVE_DPT("M_APPROVE_DPT", R.drawable.gui_tien_gui, vn.com.bidv.localization.R.string.tien_gui_va_dau_tu, 0, IBankMainRouting.TransactionApprovalRoute.TransactionApprovalMainRoute.routeWithArgument.replace("{${ROUTE_ID}}", TransactionRouteID.DepositInvestApproval.deeplinkRouteID)),
    M_APPROVE_DPT_SEND("M_APPROVE_DPT_SEND", R.drawable.gui_tien_gui, vn.com.bidv.localization.R.string.gui_tien, 0, routeID =  IBankMainRouting.TransactionApprovalRoute.TransactionApprovalMainRoute.routeWithArgument.replace("{${ROUTE_ID}}",
        TransactionRouteID.DepositApproval.deeplinkRouteID
    )),
    M_APPROVE_DPT_WDW("M_APPROVE_DPT_WDW", R.drawable.rut_tien_gui, vn.com.bidv.localization.R.string.rut_tien, 0, routeID = IBankMainRouting.TransactionApprovalRoute.TransactionApprovalMainRoute.routeWithArgument.replace("{${ROUTE_ID}}",
        TransactionRouteID.WithdrawApproval.deeplinkRouteID
    )),
    M_APPROVE_DPT_RCL("M_APPROVE_DPT_RCL", R.drawable.thu_hoi_giao_dich, vn.com.bidv.localization.R.string.thu_hoi_giao_dich_gui_tien, 0,IBankMainRouting.TransactionApprovalRoute.TransactionApprovalMainRoute.routeWithArgument.replace("{${ROUTE_ID}}", TransactionRouteID.DepositRecallApproval.deeplinkRouteID)),
    M_APPROVE_RQT("M_APPROVE_RQT", R.drawable.yeu_cau_tra_soat, vn.com.bidv.localization.R.string.yeu_cau_tra_soat_va_gui_chung_tu, 0, IBankMainRouting.TransactionApprovalRoute.TransactionApprovalMainRoute.routeWithArgument.replace("{${ROUTE_ID}}", TransactionRouteID.VerifyRequestApproval.deeplinkRouteID)),
    M_APPROVE_RQT_CHECK("M_APPROVE_RQT_CHECK", R.drawable.tra_soat, vn.com.bidv.localization.R.string.tra_soat, 0, routeID = IBankMainRouting.TransactionApprovalRoute.TransactionApprovalMainRoute.routeWithArgument.replace("{${ROUTE_ID}}", TransactionRouteID.PaymentCheckApproval.deeplinkRouteID)),
    M_ACCOUNT("M_ACCOUNT", R.drawable.tai_khoan, vn.com.bidv.localization.R.string.tai_khoan, 0),
    M_ACCOUNT_QUERY("M_ACCOUNT_QUERY", R.drawable.truy_van_tai_khoan, vn.com.bidv.localization.R.string.truy_van_tai_khoan, 0, HomepageRoute.ACCOUNT_INQUIRY_ROUTE),
    M_ACCOUNT_QUERY_DDA("M_ACCOUNT_QUERY_DDA", R.drawable.tai_khoan, vn.com.bidv.localization.R.string.tai_khoan_thanh_toan, 0, IBankMainRouting.InquiryRoute.PaymentAccountList.route),
    M_ACCOUNT_QUERY_CD("M_ACCOUNT_QUERY_CD", R.drawable.gui_tien_gui, vn.com.bidv.localization.R.string.tai_khoan_tien_gui_co_ky_han, 0,IBankMainRouting.InquiryRoute.DepositAccountList.route),
    M_ACCOUNT_QUERY_LN("M_ACCOUNT_QUERY_LN", R.drawable.tin_dung_va_bao_lanh, vn.com.bidv.localization.R.string.tai_khoan_tien_vay, 0, IBankMainRouting.InquiryRoute.LoanAccountList.route),
    M_ACCOUNT_QUERY_BG("M_ACCOUNT_QUERY_BG", R.drawable.bao_lanh, vn.com.bidv.localization.R.string.bao_lanh, 0,IBankMainRouting.InquiryRoute.GuaranteeAccountList.route),
    M_ACCOUNT_STT("M_ACCOUNT_STT", R.drawable.sao_ke_va_xac_nhan_ngan_hang, vn.com.bidv.localization.R.string.sao_ke_va_xac_nhan_cua_ngan_hang, 0, IBankMainRouting.StatementRoute.StatementManagerRoute.route),
    M_ACCOUNT_STT_ET("M_ACCOUNT_STT_ET", R.drawable.sao_ke_giao_dich, vn.com.bidv.localization.R.string.sao_ke_tung_lan, 0,IBankMainRouting.StatementRoute.StatementManagerRoute.route),
    M_ACCOUNT_STT_PRC("M_ACCOUNT_STT_PRC", R.drawable.sao_ke_giao_dich, vn.com.bidv.localization.R.string.sao_ke_dinh_ky, 0,IBankMainRouting.StatementRoute.StatementPeriodicRoute.route),
    M_ACCOUNT_STT_RCN("M_ACCOUNT_STT_RCN", R.drawable.circle, vn.com.bidv.localization.R.string.phieu_doi_chieu_tai_khoan, 0),
    M_ACCOUNT_STT_CFM("M_ACCOUNT_STT_CFM", R.drawable.circle, vn.com.bidv.localization.R.string.phieu_xac_nhan_so_du, 0),
    M_PAYMENT("M_PAYMENT", R.drawable.chuyen_tien_trong_nuoc, vn.com.bidv.localization.R.string.chuyen_tien_va_thanh_toan, 0),
    M_PAYMENT_DMC("M_PAYMENT_DMC", R.drawable.chuyen_tien_trong_nuoc, vn.com.bidv.localization.R.string.chuyen_tien_trong_nuoc, 0, IBankMainRouting.TransferRoute.TransferMainRoute.route),
    M_PAYMENT_BULK("M_PAYMENT_BULK", R.drawable.thanh_toan_bang_ke, vn.com.bidv.localization.R.string.thanh_toan_bang_ke, 0, IBankMainRouting.PaymentBulkRoute.ManageRoute.route),
    M_PAYMENT_SLY("M_PAYMENT_SLY", R.drawable.thanh_toan_luong, vn.com.bidv.localization.R.string.thanh_toan_luong, 0, IBankMainRouting.PaymentSalaryRoute.ManageRoute.route),
    M_PAYMENT_BILL("M_PAYMENT_BILL", R.drawable.thanh_toan_hoa_don, vn.com.bidv.localization.R.string.thanh_toan_hoa_don_va_nap_tien, 0, CNRRoute.CNRMainRoute.route),
    M_PAYMENT_MNE("M_PAYMENT_MNE", R.drawable.quan_ly_giao_dich_tien_gui_tuong_lai, vn.com.bidv.localization.R.string.quan_ly_giao_dich_tuong_lai, 0, IBankMainRouting.PaymentRecallRoute.TransferRecallRoute.route),
    M_PAYMENT_QUICKLINK_DMC("M_PAYMENT_QUICKLINK_DMC", R.drawable.chuyen_tien_trong_nuoc, vn.com.bidv.localization.R.string.chuyen_tien_trong_nuoc, 0, IBankMainRouting.TransferRoute.CreateTransferRoute.route),
//    M_PAYMENT_QUICKLINK_BULK("M_PAYMENT_QUICKLINK_BULK", R.drawable.thanh_toan_bang_ke, vn.com.bidv.localization.R.string.thanh_toan_bang_ke, 0, IBankMainRouting.PaymentBulkRoute.ManageRoute.route),
//    M_PAYMENT_QUICKLINK_SLY("M_PAYMENT_QUICKLINK_SLY", R.drawable.thanh_toan_luong, vn.com.bidv.localization.R.string.thanh_toan_luong, 0, IBankMainRouting.PaymentSalaryRoute.ManageRoute.route),
    M_PAYMENT_QUICKLINK_BILL("M_PAYMENT_QUICKLINK_BILL", R.drawable.thanh_toan_hoa_don, vn.com.bidv.localization.R.string.thanh_toan_hoa_don_va_nap_tien, 0, CNRRoute.CreateInvoiceScreenRoute.route),
    M_DEPOSIT("M_DEPOSIT", R.drawable.gui_tien_gui, vn.com.bidv.localization.R.string.tien_gui_va_dau_tu, 0),
    M_DEPOSIT_SEND("M_DEPOSIT_SEND", R.drawable.gui_tien_gui, vn.com.bidv.localization.R.string.gui_tien,0, IBankMainRouting.DepositRoute.DepositListRoute.route),
    M_DEPOSIT_WDW("M_DEPOSIT_WDW", R.drawable.rut_tien_gui, vn.com.bidv.localization.R.string.rut_tien,0, IBankMainRouting.DepositRoute.DepositWithDrawalListRoute.route),
    M_DEPOSIT_RQT("M_DEPOSIT_RQT", R.drawable.quan_ly_giao_dich_tien_gui_tuong_lai, vn.com.bidv.localization.R.string.quan_ly_giao_dich_tien_gui_tuong_lai, 0, IBankMainRouting.RevokeTransactionRoute.MakerTransactionManagerRoute.route),
    M_DEPOSIT_QUICKLINK_SEND("M_DEPOSIT_QUICKLINK_SEND", R.drawable.gui_tien_gui, vn.com.bidv.localization.R.string.gui_tien, 0, IBankMainRouting.DepositRoute.DepositCreateTransFlowRoute.route),
    M_DEPOSIT_QUICKLINK_WDW("M_DEPOSIT_QUICKLINK_WDW", R.drawable.rut_tien_gui, vn.com.bidv.localization.R.string.rut_tien, 0, IBankMainRouting.DepositRoute.WithdrawalCreateTransFlowRoute.route),
    M_MANAGE("M_MANAGE", R.drawable.quan_ly_nguoi_dung_va_dich_vu, vn.com.bidv.localization.R.string.quan_ly_nguoi_dung_va_dich_vu, 0),
    M_MANAGE_PENDING("M_MANAGE_PENDING", R.drawable.quan_ly_yeu_cau_cho_duyet, vn.com.bidv.localization.R.string.quan_ly_yeu_cau_cho_duyet, 0, IBankMainRouting.AuthRoutes.ManageApprovalRequestsRoute.route),
    M_MANAGE_PENDING_APPROVE("M_MANAGE_PENDING_APPROVE", R.drawable.quan_ly_yeu_cau_cho_duyet, vn.com.bidv.localization.R.string.yeu_cau_quan_tri, 0),
    M_MANAGE_LIMIT("M_MANAGE_LIMIT", R.drawable.quan_ly_dich_vu_va_han_muc, vn.com.bidv.localization.R.string.quan_ly_dich_vu_va_han_muc, 0, IBankMainRouting.StatementRoute.StatementPeriodicRoute.route),
    M_UTILITY("M_UTILITY", R.drawable.tien_ich, vn.com.bidv.localization.R.string.tien_ich, 0),
    M_UTILITY_LUP("M_UTILITY_LUP", R.drawable.circle, vn.com.bidv.localization.R.string.tra_cuu, 0),
    M_UTILITY_LUP_CTT("M_UTILITY_LUP_CTT", R.drawable.quan_ly_danh_ba_thu_huong, vn.com.bidv.localization.R.string.quan_ly_danh_ba_thu_huong, vn.com.bidv.localization.R.string.tra_cuu, IBankMainRouting.TransferRoute.BeneficiaryList.route),
    M_UTILITY_LUP_RPT("M_UTILITY_LUP_RPT",R.drawable.bao_cao_giao_dich,vn.com.bidv.localization.R.string.bao_cao_giao_dich,vn.com.bidv.localization.R.string.tra_cuu, IBankMainRouting.TransactionReportRoute.TransactionReportMainRoute.route),
    M_RPT_SEND("M_RPT_SEND", R.drawable.gui_tien_gui, vn.com.bidv.localization.R.string.gui_tien, 0, IBankMainRouting.TransactionReportRoute.TransactionReportMainRoute.routeWithArgument.replace("{${ROUTE_ID}}", TransactionRouteID.DepositReport.deeplinkRouteID)),
    M_RPT_WDW("M_RPT_WDW", R.drawable.rut_tien_gui, vn.com.bidv.localization.R.string.rut_tien, 0, IBankMainRouting.TransactionReportRoute.TransactionReportMainRoute.routeWithArgument.replace("{${ROUTE_ID}}", TransactionRouteID.WithdrawReport.deeplinkRouteID)),
    M_RPT_DMC("M_RPT_DMC", R.drawable.chuyen_tien_trong_nuoc, vn.com.bidv.localization.R.string.chuyen_tien_trong_nuoc, 0, IBankMainRouting.TransactionReportRoute.TransactionReportMainRoute.routeWithArgument.replace("{${ROUTE_ID}}", TransactionRouteID.PaymentReport.deeplinkRouteID)),
    M_RPT_BULK("M_RPT_BULK", R.drawable.thanh_toan_bang_ke, vn.com.bidv.localization.R.string.thanh_toan_bang_ke, 0, IBankMainRouting.TransactionReportRoute.TransactionReportMainRoute.routeWithArgument.replace("{${ROUTE_ID}}", TransactionRouteID.PaymentBulkReport.deeplinkRouteID)),
    M_RPT_SLY("M_RPT_SLY", R.drawable.thanh_toan_luong, vn.com.bidv.localization.R.string.thanh_toan_luong, 0, IBankMainRouting.TransactionReportRoute.TransactionReportMainRoute.routeWithArgument.replace("{${ROUTE_ID}}", TransactionRouteID.PaymentSalaryReport.deeplinkRouteID)),
    M_RPT_BILL("M_RPT_BILL", R.drawable.thanh_toan_hoa_don, vn.com.bidv.localization.R.string.thanh_toan_hoa_don_va_nap_tien, 0, routeID = IBankMainRouting.TransactionReportRoute.TransactionReportMainRoute.routeWithArgument.replace("{${ROUTE_ID}}", TransactionRouteID.CnrReport.deeplinkRouteID)) ,
    M_RPT_MNE("M_RPT_MNE", R.drawable.quan_ly_lenh_tuong_lai, vn.com.bidv.localization.R.string.quan_ly_lenh_chuyen_tien_dat_lich, 0, IBankMainRouting.TransactionReportRoute.TransactionReportMainRoute.routeWithArgument.replace("{${ROUTE_ID}}", TransactionRouteID.ScheduleTransferReport.deeplinkRouteID)),
    M_RPT_CHECK("M_RPT_CHECK", R.drawable.tra_soat, vn.com.bidv.localization.R.string.tra_soat, 0, IBankMainRouting.TransactionReportRoute.TransactionReportMainRoute.routeWithArgument.replace("{${ROUTE_ID}}", TransactionRouteID.TransferCheckReport.deeplinkRouteID)),
    M_UTILITY_LUP_TPL("M_UTILITY_LUP_TPL", R.drawable.quan_ly_mau_giao_dich, vn.com.bidv.localization.R.string.quan_ly_mau_giao_dich, vn.com.bidv.localization.R.string.tra_cuu, IBankMainRouting.TemplateTransactionRoute.TemplateTransactionMainRoute.route),
    M_UTILITY_LUP_IR("M_UTILITY_LUP_IR", R.drawable.lai_suat, vn.com.bidv.localization.R.string.lai_suat_tien_gui, vn.com.bidv.localization.R.string.tra_cuu, UtilitiesRoute.DepositInterestRateRoute.route),
    M_UTILITY_LUP_ER("M_UTILITY_LUP_ER", R.drawable.ty_gia, vn.com.bidv.localization.R.string.ty_gia_ngan_hang, vn.com.bidv.localization.R.string.tra_cuu, UtilitiesRoute.ExchangeRateRoute.route),
    M_UTILITY_LUP_FEE("M_UTILITY_LUP_FEE", R.drawable.truy_van_bieu_phi, vn.com.bidv.localization.R.string.bieu_phi, vn.com.bidv.localization.R.string.tra_cuu, UtilitiesRoute.FeeInquiryRoute.route),
    M_UTILITY_TL("M_UTILITY_TL", R.drawable.circle, vn.com.bidv.localization.R.string.cong_cu, 0),
    M_UTILITY_TL_CK("M_UTILITY_TL_CK", R.drawable.tra_soat, vn.com.bidv.localization.R.string.tra_soat, vn.com.bidv.localization.R.string.cong_cu, IBankMainRouting.PaymentInquiryRoute.PaymentInquiryScreenRoute.route),
    M_UTILITY_SPT("M_UTILITY_SPT", R.drawable.ho_tro_dvcnt, vn.com.bidv.localization.R.string.ho_tro, 0),
    M_UTILITY_SPT_USER("M_UTILITY_SPT_USER", R.drawable.huong_dan_su_dung, vn.com.bidv.localization.R.string.huong_dan_su_dung, vn.com.bidv.localization.R.string.ho_tro, CommonRoute.OpenBrowserRoute.toRoute().replace("{$URL_TYPE}", Constants.UrlType.GUIDELINE.name)),
    M_UTILITY_SPT_QTS("M_UTILITY_SPT_QTS", R.drawable.cau_hoi_thuong_gap, vn.com.bidv.localization.R.string.cau_hoi_thuong_gap, vn.com.bidv.localization.R.string.ho_tro, CommonRoute.OpenBrowserRoute.toRoute().replace("{$URL_TYPE}", Constants.UrlType.FAQ.name)),
    M_UTILITY_SPT_TM("M_UTILITY_SPT_TM", R.drawable.dieu_khoan_su_dung, vn.com.bidv.localization.R.string.dieu_khoan_dieu_kien, vn.com.bidv.localization.R.string.ho_tro, CommonRoute.OpenBrowserRoute.toRoute().replace("{$URL_TYPE}", Constants.UrlType.TERMS.name)),
    M_UTILITY_SPT_IFR("M_UTILITY_SPT_IFR", R.drawable.thong_tin_lien_he, vn.com.bidv.localization.R.string.thong_tin_lien_he, vn.com.bidv.localization.R.string.ho_tro, routeID = HomepageRoute.CONTACT_SCREEN),
    M_SET("M_SET", R.drawable.cai_dat_nguoi_dung, vn.com.bidv.localization.R.string.cai_dat_nguoi_dung, 0),
    M_SET_USER("M_SET_USER", R.drawable.thong_tin_nguoi_dung, vn.com.bidv.localization.R.string.thong_tin_nguoi_dung,0,IBankMainRouting.AuthRoutes.UserInfoRoute.route),
    M_SET_AVT("M_SET_AVT", R.drawable.cai_dat_giao_dien, vn.com.bidv.localization.R.string.cai_dat_hinh_dai_dien, 0,IBankMainRouting.HomeRoute.AvatarPickerRoute.route),
    M_SET_SCRT("M_SET_SCRT", R.drawable.cai_dat_thong_tin_bao_mat, vn.com.bidv.localization.R.string.cai_dat_thong_tin_bao_mat, 0),
    M_SET_NOTI("M_SET_NOTI", R.drawable.cai_dat_thong_bao, vn.com.bidv.localization.R.string.cai_dat_thong_bao, 0),
    M_SET_LGE("M_SET_LGE", R.drawable.cai_dat_ngon_ngu, vn.com.bidv.localization.R.string.cai_dat_ngon_ngu, 0),
    M_PUBLICSVC("M_PUBLICSVC", R.drawable.dich_vu_cong, vn.com.bidv.localization.R.string.dich_vu_cong, 0),

    //config for code
    PENDING_APPROVE_MAKER_M_PAYMENT_DMC("PENDING_APPROVE_MAKERM_PAYMENT_DMC", R.drawable.chuyen_tien_trong_nuoc_outline, vn.com.bidv.localization.R.string.chuyen_tien_trong_nuoc, 0,IBankMainRouting.TransferRoute.TransferMainRoute.routeWithArgument.replace("{${tabId}}","ALL").replace("{${STATUS_TRANSACTION}}",TransactionStatusBase.PENDING_APPROVAL.statusCode)),
    PENDING_APPROVE_MAKER_M_PAYMENT_BULK("PENDING_APPROVE_MAKERM_PAYMENT_BULK", R.drawable.thanh_toan_bang_ke_outline, vn.com.bidv.localization.R.string.thanh_toan_bang_ke, 0,IBankMainRouting.PaymentBulkRoute.ManageRoute.routeWithArgument.replace("{${STATUS_TRANSACTION}}",TransactionStatusBase.PENDING_APPROVAL.statusCode)),
    PENDING_APPROVE_MAKER_M_PAYMENT_SLY("PENDING_APPROVE_MAKERM_PAYMENT_SLY", R.drawable.thanh_toan_luong_outline, vn.com.bidv.localization.R.string.thanh_toan_luong, 0,IBankMainRouting.PaymentSalaryRoute.ManageRoute.routeWithArgument.replace("{${STATUS_TRANSACTION}}",TransactionStatusBase.PENDING_APPROVAL.statusCode)),
    PENDING_APPROVE_MAKER_M_PAYMENT_RCL("PENDING_APPROVE_MAKERM_PAYMENT_MNE_RCL", R.drawable.thu_hoi_giao_dich_outline, vn.com.bidv.localization.R.string.thu_hoi_giao_dich_tuong_lai, 0,IBankMainRouting.PaymentRecallRoute.TransferRecallRoute.routeWithArgument.replace("{${tabId}}","RECALL_TXN").replace("{${STATUS_TRANSACTION}}",TransactionStatusBase.PENDING_APPROVAL.statusCode).replace("{${PRODUCT_CODE}}","")),
    PENDING_APPROVE_MAKER_M_DEPOSIT_SEND("PENDING_APPROVE_MAKERM_DEPOSIT_SEND", R.drawable.gui_tien_gui_outline, vn.com.bidv.localization.R.string.gui_tien, 0, IBankMainRouting.DepositRoute.DepositListRoute.routeWithArgument.replace("{${TAB_ID}}","0").replace("{${STATUS_TRANSACTION}}",TransactionStatusBase.PENDING_APPROVAL.statusCode)),
    PENDING_APPROVE_MAKER_M_DEPOSIT_WDW("PENDING_APPROVE_MAKERM_DEPOSIT_WDW", R.drawable.rut_tien_gui_outline, vn.com.bidv.localization.R.string.rut_tien, 0,IBankMainRouting.DepositRoute.DepositWithDrawalListRoute.routeWithArgument.replace("{${TAB_ID}}","0").replace("{${STATUS_TRANSACTION}}",TransactionStatusBase.PENDING_APPROVAL.statusCode)),
    PENDING_APPROVE_MAKER_M_DEPOSIT_RQT_DP01("PENDING_APPROVE_MAKERM_DEPOSIT_RQT_DP01", R.drawable.thu_hoi_giao_dich_outline, vn.com.bidv.localization.R.string.thu_hoi_giao_dich_tien_gui_tuong_lai, 0,IBankMainRouting.RevokeTransactionRoute.RevokeListTransactionRoute.routeWithArgument.replace("{${TAB_ID}}","RECALL_TXN").replace("{${IBankMainRouting.RevokeTransactionRoute.STATUS_TRANSACTION}}",TransactionStatusBase.PENDING_APPROVAL.statusCode)),
    PENDING_APPROVE_MAKER_M_PAYMENT_BILL("PENDING_APPROVE_MAKERM_PAYMENT_BILL", R.drawable.thanh_toan_hoa_don_outline, vn.com.bidv.localization.R.string.thanh_toan_hoa_don_va_nap_tien, 0,CNRRoute.InvoiceAndTopUpScreenRoute.routeWithArgument.replace("{${TAB_ID}}","0").replace("{${STATUS_TRANSACTION}}",TransactionStatusBase.PENDING_APPROVAL.statusCode)),
    PENDING_APPROVE_MAKER_M_UTILITY_TL_CK_PM51("PENDING_APPROVE_MAKERM_UTILITY_TL_CK", R.drawable.tra_soat_outline, vn.com.bidv.localization.R.string.tra_soat_chuyen_tien_va_thanh_toan, 0,IBankMainRouting.PaymentInquiryRoute.PaymentInquiryScreenRoute.routeWithArgument.replace("{${STATUS_TRANSACTION}}",TransactionStatusBase.PENDING_APPROVAL.statusCode)),
    REJECT_M_PAYMENT_DMC("REJECTM_PAYMENT_DMC", R.drawable.chuyen_tien_trong_nuoc_outline, vn.com.bidv.localization.R.string.chuyen_tien_trong_nuoc, 0,IBankMainRouting.TransferRoute.TransferMainRoute.routeWithArgument.replace("{${tabId}}","PENDING").replace("{${STATUS_TRANSACTION}}",TransactionStatusBase.REJECTED.statusCode)),
    REJECT_M_PAYMENT_BULK("REJECTM_PAYMENT_BULK", R.drawable.thanh_toan_bang_ke_outline, vn.com.bidv.localization.R.string.thanh_toan_bang_ke, 0,IBankMainRouting.PaymentBulkRoute.ManageRoute.routeWithArgument.replace("{${STATUS_TRANSACTION}}",TransactionStatusBase.REJECTED.statusCode)),
    REJECT_M_PAYMENT_SLY("REJECTM_PAYMENT_SLY", R.drawable.thanh_toan_luong_outline, vn.com.bidv.localization.R.string.thanh_toan_luong, 0,IBankMainRouting.PaymentSalaryRoute.ManageRoute.routeWithArgument.replace("{${STATUS_TRANSACTION}}",TransactionStatusBase.REJECTED.statusCode)),
    REJECT_M_PAYMENT_RCL("REJECTM_PAYMENT_MNE_RCL", R.drawable.thu_hoi_giao_dich_outline, vn.com.bidv.localization.R.string.thu_hoi_giao_dich_tuong_lai, 0,IBankMainRouting.PaymentRecallRoute.TransferRecallRoute.routeWithArgument.replace("{${tabId}}","RECALL_TXN").replace("{${STATUS_TRANSACTION}}",TransactionStatusBase.REJECTED.statusCode).replace("{${PRODUCT_CODE}}","P-TTLG")),
    REJECT_M_DEPOSIT_SEND("REJECTM_DEPOSIT_SEND", R.drawable.gui_tien_gui_outline, vn.com.bidv.localization.R.string.gui_tien, 0,IBankMainRouting.DepositRoute.DepositListRoute.routeWithArgument.replace("{${TAB_ID}}","1").replace("{${STATUS_TRANSACTION}}",TransactionStatusBase.REJECTED.statusCode)),
    REJECT_M_DEPOSIT_WDW("REJECTM_DEPOSIT_WDW", R.drawable.rut_tien_gui_outline, vn.com.bidv.localization.R.string.rut_tien, 0,IBankMainRouting.DepositRoute.DepositWithDrawalListRoute.routeWithArgument.replace("{${TAB_ID}}","1").replace("{${STATUS_TRANSACTION}}",TransactionStatusBase.REJECTED.statusCode)),
    REJECT_M_DEPOSIT_RQT_DP01("REJECTM_DEPOSIT_RQT_DP01", R.drawable.thu_hoi_giao_dich_outline, vn.com.bidv.localization.R.string.thu_hoi_giao_dich_tien_gui_tuong_lai, 0,IBankMainRouting.RevokeTransactionRoute.RevokeListTransactionRoute.routeWithArgument.replace("{${TAB_ID}}","RECALL_TXN").replace("{${IBankMainRouting.RevokeTransactionRoute.STATUS_TRANSACTION}}",TransactionStatusBase.REJECTED.statusCode)),
    REJECT_M_PAYMENT_BILL("REJECTM_PAYMENT_BILL", R.drawable.thanh_toan_hoa_don_outline, vn.com.bidv.localization.R.string.thanh_toan_hoa_don_va_nap_tien, 0,CNRRoute.InvoiceAndTopUpScreenRoute.routeWithArgument.replace("{${TAB_ID}}","1").replace("{${STATUS_TRANSACTION}}",TransactionStatusBase.REJECTED.statusCode)),
    REJECT_M_UTILITY_TL_CK_PM51("REJECTM_UTILITY_TL_CK", R.drawable.tra_soat_outline, vn.com.bidv.localization.R.string.tra_soat_chuyen_tien_va_thanh_toan, 0,IBankMainRouting.PaymentInquiryRoute.PaymentInquiryScreenRoute.routeWithArgument.replace("{${STATUS_TRANSACTION}}",TransactionStatusBase.REJECTED.statusCode)),
    FUTURE_M_PAYMENT_MNE_PM("FUTUREM_PAYMENT_MNE_PM", R.drawable.chuyen_tien_trong_nuoc_outline, vn.com.bidv.localization.R.string.chuyen_tien_trong_nuoc, 0,IBankMainRouting.PaymentRecallRoute.TransferRecallRoute.routeWithArgument.replace("{${tabId}}","FUTURE_TXN").replace("{${STATUS_TRANSACTION}}","").replace("{${PRODUCT_CODE}}","P-CTTN")),
    FUTURE_M_PAYMENT_MNE_PM21("FUTUREM_PAYMENT_MNE_PM21", R.drawable.thanh_toan_bang_ke_outline, vn.com.bidv.localization.R.string.thanh_toan_bang_ke, 0,IBankMainRouting.PaymentRecallRoute.TransferRecallRoute.routeWithArgument.replace("{${tabId}}","FUTURE_TXN").replace("{${STATUS_TRANSACTION}}","").replace("{${PRODUCT_CODE}}","P-TTBK")),
    FUTURE_M_PAYMENT_MNE_PM22("FUTUREM_PAYMENT_MNE_PM22", R.drawable.thanh_toan_luong_outline, vn.com.bidv.localization.R.string.thanh_toan_luong, 0,IBankMainRouting.PaymentRecallRoute.TransferRecallRoute.routeWithArgument.replace("{${tabId}}","FUTURE_TXN").replace("{${STATUS_TRANSACTION}}","").replace("{${PRODUCT_CODE}}","P-TTLG")),
    FUTURE_M_DEPOSIT_RQT_F_DP01("FUTUREM_DEPOSIT_RQT_F_DP01", R.drawable.gui_tien_gui_outline, vn.com.bidv.localization.R.string.gui_tien, 0,IBankMainRouting.RevokeTransactionRoute.RevokeListTransactionRoute.routeWithArgument.replace("{${TAB_ID}}","FUTURE_TXN").replace(IBankMainRouting.RevokeTransactionRoute.STATUS_TRANSACTION,TransactionStatusBase.FUTURE.statusCode)),
    PENDING_APPROVE_CHECKER_M_APPROVE_PMT_DMC("PENDING_APPROVE_CHECKERM_APPROVE_PMT_DMC", R.drawable.chuyen_tien_trong_nuoc_outline, vn.com.bidv.localization.R.string.chuyen_tien_trong_nuoc, 0,IBankMainRouting.TransactionApprovalRoute.TransactionApprovalMainRoute.routeWithArgument.replace("{${ROUTE_ID}}", TransactionRouteID.PaymentApproval.deeplinkRouteID)),
    PENDING_APPROVE_CHECKER_M_APPROVE_PMT_BULK("PENDING_APPROVE_CHECKERM_APPROVE_PMT_BULK", R.drawable.thanh_toan_bang_ke_outline, vn.com.bidv.localization.R.string.thanh_toan_bang_ke, 0,IBankMainRouting.TransactionApprovalRoute.TransactionApprovalMainRoute.routeWithArgument.replace("{${ROUTE_ID}}", TransactionRouteID.PaymentBulkApproval.deeplinkRouteID)),
    PENDING_APPROVE_CHECKER_M_APPROVE_PMT_SLY("PENDING_APPROVE_CHECKERM_APPROVE_PMT_SLY", R.drawable.thanh_toan_luong_outline, vn.com.bidv.localization.R.string.thanh_toan_luong, 0,IBankMainRouting.TransactionApprovalRoute.TransactionApprovalMainRoute.routeWithArgument.replace("{${ROUTE_ID}}", TransactionRouteID.PaymentSalaryApproval.deeplinkRouteID)),
    PENDING_APPROVE_CHECKER_M_APPROVE_PMT_RCL("PENDING_APPROVE_CHECKERM_APPROVE_PMT_RCL", R.drawable.thu_hoi_giao_dich_outline, vn.com.bidv.localization.R.string.thu_hoi_giao_dich, 0,IBankMainRouting.TransactionApprovalRoute.TransactionApprovalMainRoute.routeWithArgument.replace("{${ROUTE_ID}}", TransactionRouteID.TransferRecallApproval.deeplinkRouteID)),
    PENDING_APPROVE_CHECKER_M_APPROVE_RQT_CHECK("PENDING_APPROVE_CHECKERM_APPROVE_RQT_CHECK", R.drawable.tra_soat_outline, vn.com.bidv.localization.R.string.tra_soat_chuyen_tien_va_thanh_toan, 0,IBankMainRouting.TransactionApprovalRoute.TransactionApprovalMainRoute.routeWithArgument.replace("{${ROUTE_ID}}", TransactionRouteID.PaymentCheckApproval.deeplinkRouteID)),
    PENDING_APPROVE_CHECKER_M_APPROVE_DPT_SEND("PENDING_APPROVE_CHECKERM_APPROVE_DPT_SEND", R.drawable.gui_tien_gui_outline, vn.com.bidv.localization.R.string.gui_tien, 0,IBankMainRouting.TransactionApprovalRoute.TransactionApprovalMainRoute.routeWithArgument.replace("{${ROUTE_ID}}", TransactionRouteID.DepositApproval.deeplinkRouteID)),
    PENDING_APPROVE_CHECKER_M_APPROVE_DPT_WDW("PENDING_APPROVE_CHECKERM_APPROVE_DPT_WDW", R.drawable.rut_tien_gui_outline, vn.com.bidv.localization.R.string.rut_tien, 0,IBankMainRouting.TransactionApprovalRoute.TransactionApprovalMainRoute.routeWithArgument.replace("{${ROUTE_ID}}", TransactionRouteID.WithdrawApproval.deeplinkRouteID)),
    PENDING_APPROVE_CHECKER_M_APPROVE_DPT_RCL("PENDING_APPROVE_CHECKERM_APPROVE_DPT_RCL", R.drawable.thu_hoi_giao_dich_outline, vn.com.bidv.localization.R.string.thu_hoi_giao_dich_gui_tien, 0,IBankMainRouting.TransactionApprovalRoute.TransactionApprovalMainRoute.routeWithArgument.replace("{${ROUTE_ID}}", TransactionRouteID.DepositRecallApproval.deeplinkRouteID)),
    PENDING_APPROVE_CHECKER_M_APPROVE_PMT_BILL("PENDING_APPROVE_CHECKERM_APPROVE_PMT_BILL", R.drawable.thanh_toan_hoa_don_outline, vn.com.bidv.localization.R.string.thanh_toan_hoa_don_va_nap_tien, 0,IBankMainRouting.TransactionApprovalRoute.TransactionApprovalMainRoute.routeWithArgument.replace("{${ROUTE_ID}}", TransactionRouteID.CnrApproval.deeplinkRouteID)),
    PENDING_APPROVE_CHECKER_M_MANAGE_PENDING_APPROVE("PENDING_APPROVE_CHECKERM_MANAGE_PENDING_APPROVE", R.drawable.quan_ly_yeu_cau_cho_duyet_outline, vn.com.bidv.localization.R.string.yeu_cau_quan_tri, 0,IBankMainRouting.AuthRoutes.ManageApprovalRequestsRoute.route),
    PENDING_APPROVE_CHECKER_M_APPROVE_RQT_PM51("PENDING_APPROVE_CHECKERM_APPROVE_RQT_PM51", R.drawable.tra_soat_outline, vn.com.bidv.localization.R.string.tra_soat_chuyen_tien_va_thanh_toan, 0,IBankMainRouting.TransactionApprovalRoute.TransactionApprovalMainRoute.routeWithArgument.replace("{${ROUTE_ID}}", TransactionRouteID.PaymentCheckApproval.deeplinkRouteID)),
    CUSTOM_WIDGET("CUSTOM_WIDGET",R.drawable.nav_settings_outline,vn.com.bidv.localization.R.string.tuy_chinh,0),
    FUTURE("FUTURE",R.drawable.circle, vn.com.bidv.localization.R.string.hieu_luc_tuong_lai,0),
    REJECT("REJECT",R.drawable.circle, vn.com.bidv.localization.R.string.tu_choi,0),
    PENDING_APPROVE_MAKER("PENDING_APPROVE_MAKER",R.drawable.circle, vn.com.bidv.localization.R.string.chua_duoc_duyet,0),
    PENDING_APPROVE_CHECKER("PENDING_APPROVE_CHECKER",R.drawable.circle, vn.com.bidv.localization.R.string.cho_duyet,0),
    DEFAULT("DEFAULT", R.drawable.circle, vn.com.bidv.localization.R.string.khong_co_du_lieu, 0);

    companion object {
        fun fromCode(code: String): PermissionCode {
            return entries.find { it.code == code } ?: DEFAULT
        }
    }
}

@Serializable
data class PermissionResUI(val permissionResDMO: PermissionResDMO) :
    BaseUIModel<PermissionResDMO>(data = permissionResDMO) {
    val icon: Int
        get() = PermissionCode.fromCode(data.code.orEmpty()).iconRes

    val title: Int
        get() = PermissionCode.fromCode(data.code.orEmpty()).titleRes

    val titleGroup: Int
        get() = PermissionCode.fromCode(data.code.orEmpty()).titleGroupRes

    val routeID: String?
        get() = PermissionCode.fromCode(data.code.orEmpty()).routeID

    val code: String
        get() = PermissionCode.fromCode(data.code.orEmpty()).code
}

fun PermissionResDMO.toPresenterModel(): PermissionResUI {
    return PermissionResUI(
        permissionResDMO = this
    )
}

fun List<PermissionResDMO>.removeDefaultModel(): List<PermissionResDMO> {
    return this.filter {
        it.toPresenterModel().code != PermissionCode.DEFAULT.code
    }
}
