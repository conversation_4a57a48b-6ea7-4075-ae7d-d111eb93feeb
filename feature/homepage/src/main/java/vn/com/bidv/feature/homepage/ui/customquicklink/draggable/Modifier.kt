package vn.com.bidv.feature.homepage.ui.customquicklink.draggable

import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.pointerInput

fun Modifier.draggable(
    draggableState: DraggableState<*>,
    key: Any,
) = then(
    Modifier
        .pointerInput(Unit) {
            detectDragGestures(
                onDragStart = {
                    draggableState.onDragStart(key)
                },
                onDragEnd = {
                    draggableState.onDragCanceled()
                },
                onDragCancel = {
                    draggableState.onDragCanceled()
                },
            ) { change, dragAmount ->
                change.consume()
                draggableState.onDrag(dragAmount)
            }
        }
)

fun <T> MutableList<T>.rearrangeItems(fromIndex: Int, toIndex: Int): MutableList<T> {
    if (fromIndex !in 0 until size || toIndex !in 0 until size || fromIndex == toIndex) return this

    val item = this[fromIndex]
    if (fromIndex < toIndex) {
        for (i in fromIndex until toIndex) {
            this[i] = this[i + 1]
        }
    } else {
        for (i in fromIndex downTo toIndex + 1) {
            this[i] = this[i - 1]
        }
    }
    this[toIndex] = item
    return this
}

