package vn.com.bidv.feature.homepage.ui.paymentaccountbalance.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.feature.homepage.constants.HomePageConstants
import vn.com.bidv.feature.homepage.common.enum.CurrencyCode

@Composable
fun AccountBalanceCardItem(
    modifier: Modifier = Modifier,
    currencyCode: CurrencyCode,
    balance: String,
    shouldHideBalanceInfo: Boolean = false,
    hasDataFetchedOnce: Boolean = false,
    cardItemWidth: Dp = 0.dp,
    cardItemHeight: Dp = 0.dp,
) {
    val colorSchema = LocalColorScheme.current
    val typography = LocalTypography.current
    val imageIconValue =
        if (!hasDataFetchedOnce) R.drawable.icon_currency_hidden else currencyCode.currencyIcon
    val currencyCodeValue = if (!hasDataFetchedOnce) "" else currencyCode.currencyCode
    val balanceValue =
        if (!hasDataFetchedOnce || shouldHideBalanceInfo) HomePageConstants.NOT_SHOW_DATA_MONEY else balance

    Box(
        modifier =
            modifier
                .widthIn(min = cardItemWidth)
                .heightIn(min = cardItemHeight)
                .clip(shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL))
                .background(color = colorSchema.bgMainPrimary),
        contentAlignment = Alignment.CenterStart,
    ) {
        Column(
            horizontalAlignment = Alignment.Start,
            modifier =
                Modifier.padding(
                    horizontal = IBSpacing.spacingM,
                    vertical = IBSpacing.spacingS,
                ),
        ) {
            Image(
                modifier =
                    Modifier
                        .size(IBSpacing.spacing2xl),
                imageVector =
                    ImageVector.vectorResource(
                        imageIconValue,
                    ),
                contentDescription = currencyCodeValue,
            )
            Text(
                text = currencyCodeValue,
                modifier = Modifier.padding(top = IBSpacing.spacingS),
                style = typography.bodyBody_m,
                color = LocalColorScheme.current.contentMainTertiary,
            )
            Text(
                text = balanceValue,
                style = typography.titleTitle_m,
            )
        }
    }
}

@Preview(showBackground = false)
@Composable
fun AccountBalanceCardItemPreview() {
    val mockBalance = "1,000,000"
    Column {
        AccountBalanceCardItem(
            modifier = Modifier,
            currencyCode = CurrencyCode.IC_CURRENCY_ARAB,
            balance = mockBalance,
            hasDataFetchedOnce = false,
        )
        Spacer(modifier = Modifier.size(10.dp))
        AccountBalanceCardItem(
            modifier = Modifier,
            currencyCode = CurrencyCode.IC_CURRENCY_USD,
            balance = mockBalance,
            shouldHideBalanceInfo = true,
            hasDataFetchedOnce = true,
        )
        Spacer(modifier = Modifier.size(10.dp))
        AccountBalanceCardItem(
            modifier = Modifier,
            currencyCode = CurrencyCode.IC_CURRENCY_ARAB,
            balance = mockBalance,
            shouldHideBalanceInfo = false,
            hasDataFetchedOnce = true,
        )
    }
}
