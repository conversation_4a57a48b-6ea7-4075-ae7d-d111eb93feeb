package vn.com.bidv.feature.homepage.constants

enum class WidgetType(val titleId: Int, val resId: Int) {
    Widget_PaymetAcctDetail(titleId = vn.com.bidv.localization.R.string.chi_tiet_tai_khoan_thanh_toan, resId = vn.com.bidv.designsystem.R.drawable.chi_tiet_tk),
    Widget_NotiDueAmount(titleId = vn.com.bidv.localization.R.string.thong_bao_cac_khoan_den_han, resId = vn.com.bidv.designsystem.R.drawable.gia_han_the),
    Widget_AvailblePmtBal(titleId = vn.com.bidv.localization.R.string.so_du_thanh_toan_kha_dung, resId = vn.com.bidv.designsystem.R.drawable.chuyen_tien_quoc_te),
    Widget_ExchangeRate(titleId = vn.com.bidv.localization.R.string.ty_gia, resId = vn.com.bidv.designsystem.R.drawable.ty_gia),
    Widget_OverView(titleId = vn.com.bidv.localization.R.string.tong_quan, resId = vn.com.bidv.designsystem.R.drawable.danh_sach_tk),
    Widget_TransManage(titleId = vn.com.bidv.localization.R.string.quan_ly_giao_dich, resId = vn.com.bidv.designsystem.R.drawable.trang_thai_giao_dich)
}