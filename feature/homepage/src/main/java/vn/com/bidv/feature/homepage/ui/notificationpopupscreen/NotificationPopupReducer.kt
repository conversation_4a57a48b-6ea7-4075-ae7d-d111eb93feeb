package vn.com.bidv.feature.homepage.ui.notificationpopupscreen

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.homepage.constants.NotificationPopupConstants.STATUS_UPDATE_DISPLAY
import vn.com.bidv.feature.homepage.constants.NotificationPopupConstants.STATUS_UPDATE_LINK
import vn.com.bidv.feature.homepage.domain.notificationpopupdmo.ModelListPopupNotifyStatementResponseDMO
import vn.com.bidv.feature.homepage.ui.notificationpopupscreen.modelui.ListModelNotificationPopupUI

class NotificationPopupReducer :
    Reducer<NotificationPopupReducer.NotificationPopupViewState,
            NotificationPopupReducer.NotificationPopupViewEvent,
            NotificationPopupReducer.NotificationPopupViewEffect> {

    @Immutable
    sealed class NotificationPopupViewState(
        val listModelNotificationPopupUI: ListModelNotificationPopupUI
    ) : ViewState {
        data class Result(
            val viewData: ListModelNotificationPopupUI = ListModelNotificationPopupUI.getDefault(),
        ) : NotificationPopupViewState(viewData)
    }

    @Immutable
    sealed interface NotificationPopupViewEvent : ViewEvent {
        data class OnGetNotificationPopup(
            val isFromStorage: Boolean = false
        ) : NotificationPopupViewEvent

        data class GetNotificationPopupSuccess(
            val listNotificationPopupResponseDMO: ModelListPopupNotifyStatementResponseDMO?
        ) : NotificationPopupViewEvent

        data object GetNotificationPopupFail : NotificationPopupViewEvent

        data object OnNextPopup : NotificationPopupViewEvent

        data object OnButtonFeedbackClicked : NotificationPopupViewEvent

        data object OnButtonDisplayClicked : NotificationPopupViewEvent

        data object OnButtonLinkClicked : NotificationPopupViewEvent

        data class UpdateStatusFeedbackPopupFail(
            val errorMessage: String?
        ) : NotificationPopupViewEvent

        data object UpdateStatusFeedbackPopupSuccess : NotificationPopupViewEvent
    }

    @Immutable
    sealed interface NotificationPopupViewEffect : SideEffect {
        data class GetNotificationPopup(
            val isFromStorage: Boolean
        ) : NotificationPopupViewEffect

        data class UpdateStatusDisplayPopup(
            val id: Long?,
            val type: String
        ) : NotificationPopupViewEffect

        data class RemovePopupInCacheById(
            val id: Long?
        ) : NotificationPopupViewEffect

        data class UpdateStatusFeedbackPopup(
            val id: Long?
        ) : NotificationPopupViewEffect

        data class UpdateStatusFeedbackPopupStatusFail(
            val errorMessage: String?
        ) : NotificationPopupViewEffect, UIEffect
    }

    override fun reduce(
        previousState: NotificationPopupViewState,
        event: NotificationPopupViewEvent,
    ): Pair<NotificationPopupViewState, NotificationPopupViewEffect?> {
        val previousUiState = previousState.listModelNotificationPopupUI

        return when (event) {
            is NotificationPopupViewEvent.OnGetNotificationPopup -> {
                previousState to NotificationPopupViewEffect.GetNotificationPopup(
                    isFromStorage = event.isFromStorage
                )
            }

            is NotificationPopupViewEvent.GetNotificationPopupSuccess -> {
                val notificationPopupDMO = event.listNotificationPopupResponseDMO
                NotificationPopupViewState.Result(
                    viewData = previousUiState.copy(
                        totalPopup = notificationPopupDMO?.total,
                        isPopupLoaded = true,
                        listNotificationPopup = notificationPopupDMO?.listNotificationPopup
                    )
                ) to null
            }

            is NotificationPopupViewEvent.GetNotificationPopupFail -> {
                NotificationPopupViewState.Result(
                    viewData = previousUiState.copy(isPopupLoaded = true)
                ) to null
            }

            is NotificationPopupViewEvent.OnNextPopup -> {
                NotificationPopupViewState.Result(
                    viewData = previousUiState.nextPopup()
                ) to NotificationPopupViewEffect.RemovePopupInCacheById(
                    id = previousUiState.getCurrentPopup()?.id
                )
            }

            is NotificationPopupViewEvent.OnButtonDisplayClicked -> {
                NotificationPopupViewState.Result(
                    viewData = previousUiState.nextPopup()
                ) to NotificationPopupViewEffect.UpdateStatusDisplayPopup(
                    id = previousUiState.getCurrentPopup()?.id,
                    type = STATUS_UPDATE_DISPLAY
                )
            }

            is NotificationPopupViewEvent.OnButtonFeedbackClicked -> {
                previousState to NotificationPopupViewEffect.UpdateStatusFeedbackPopup(
                    id = previousUiState.getCurrentPopup()?.id
                )
            }

            is NotificationPopupViewEvent.OnButtonLinkClicked -> {
                previousState to NotificationPopupViewEffect.UpdateStatusDisplayPopup(
                    id = previousUiState.getCurrentPopup()?.id,
                    type = STATUS_UPDATE_LINK
                )
            }

            is NotificationPopupViewEvent.UpdateStatusFeedbackPopupSuccess -> {
                NotificationPopupViewState.Result(
                    viewData = previousUiState.nextPopup()
                ) to null
            }

            is NotificationPopupViewEvent.UpdateStatusFeedbackPopupFail -> {
                previousState to NotificationPopupViewEffect.UpdateStatusFeedbackPopupStatusFail(
                    errorMessage = event.errorMessage
                )
            }
        }
    }
}
