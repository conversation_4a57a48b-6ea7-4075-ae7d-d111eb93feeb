package vn.com.bidv.feature.homepage.di

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import vn.com.bidv.feature.common.data.utilitiesnotify.apis.UtilitiesNotifyStatementsApi
import vn.com.bidv.feature.homepage.data.NotificationPopupRepository
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class NotificationModule {

    @Provides
    @Singleton
    fun provideNotificationsPopupRepository(
        service: UtilitiesNotifyStatementsApi
    ): NotificationPopupRepository = NotificationPopupRepository(service)


}