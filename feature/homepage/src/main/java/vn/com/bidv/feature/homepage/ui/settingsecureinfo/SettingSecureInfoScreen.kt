package vn.com.bidv.feature.homepage.ui.settingsecureinfo

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.navigation.NavHostController
import vn.com.bidv.common.extenstion.isNotNull
import vn.com.bidv.common.extenstion.isNull
import vn.com.bidv.common.utils.CollectSideEffect
import vn.com.bidv.designsystem.component.IBankEmptyState
import vn.com.bidv.designsystem.component.datadisplay.badge.IBankBadgeLabel
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelColor
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelSize
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelType
import vn.com.bidv.designsystem.component.dataentry.IBankSwitch
import vn.com.bidv.designsystem.component.functionmenuitem.SubMenuItem
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.common.constants.Constants
import vn.com.bidv.feature.homepage.domain.model.SmartOtpSettingDMO
import vn.com.bidv.feature.homepage.navigation.HomePageNavigationHelper
import vn.com.bidv.feature.homepage.ui.settingsecureinfo.SettingSecureInfoReducer.SettingSecureInfoViewEvent
import vn.com.bidv.feature.homepage.ui.settingsecureinfo.SettingSecureInfoReducer.SettingSecureInfoViewState
import vn.com.bidv.feature.homepage.ui.settingsecureinfo.model.InfoItem
import vn.com.bidv.feature.homepage.ui.settingsecureinfo.model.ModelSettingSecureInfoConfigUI
import vn.com.bidv.feature.homepage.ui.settingsecureinfo.model.ThumbContentType
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.ui.component.snackbar.SnackBarPosition
import vn.com.bidv.sdkbase.ui.component.snackbar.pinSnackBarPosition
import vn.com.bidv.designsystem.R as RDesignSystem
import vn.com.bidv.localization.R as RLocalization

@Composable
fun SettingSecureInfoScreen(navController: NavHostController) {
    val vm: SettingSecureInfoViewModel = hiltViewModel()
    val lifecycleOwner = LocalLifecycleOwner.current
    val lifecycleState by lifecycleOwner.lifecycle.currentStateFlow.collectAsState()

    BaseScreen(
        navController = navController,
        viewModel = vm,
        renderContent = { uiState, onEvent ->
            if (!uiState.isInitSuccess) {
                onEvent(SettingSecureInfoViewEvent.OnInitSetting)
            } else {
                LaunchedEffect(lifecycleState) {
                    when (lifecycleState) {
                        Lifecycle.State.RESUMED -> {
                            onEvent(SettingSecureInfoViewEvent.OnInitSetting)
                        }
                        else -> {}
                    }
                }
                SubscribeVerifyBiometric(vm, onEvent)
                SettingSecureInfoContent(
                    uiState = uiState,
                    onEvent = onEvent,
                    navController = navController
                )
            }
        },
        handleSideEffect = { sideEffect ->
        },
        topAppBarType = TopAppBarType.Title,
        backgroundColor = LocalColorScheme.current.bgMainPrimary,
        topAppBarConfig = TopAppBarConfig(
            titleTopAppBar = stringResource(RLocalization.string.cai_dat_thong_tin_bao_mat)
        )
    )
}

@Composable
fun SubscribeVerifyBiometric(
    viewModel: SettingSecureInfoViewModel,
    onEvent: (SettingSecureInfoViewEvent) -> Unit
) {
    CollectSideEffect(viewModel.subscribeShareData(Constants.Biometric.BIOMETRIC_ACTION_SUCCESS)) {
        if (it.data == Constants.Biometric.BIOMETRIC_TURNON_SUCCESS) {
            onEvent(SettingSecureInfoViewEvent.OnTurnOnBiometricSuccess)
        }
        if (it.data == Constants.Biometric.BIOMETRIC_TURNOFF_SUCCESS) {
            onEvent(SettingSecureInfoViewEvent.OnTurnOffBiometricSuccess)
        }
    }

}

@Composable
private fun SettingSecureInfoContent(
    uiState: SettingSecureInfoViewState,
    onEvent: (SettingSecureInfoViewEvent) -> Unit,
    navController: NavHostController
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = IBSpacing.spacingM, vertical = IBSpacing.spacingS)
            .pinSnackBarPosition(snackBarPosition = SnackBarPosition.BottomIn, margin = 33.dp)
    ) {
        Column(modifier = Modifier.fillMaxSize()) {

            ManageSignInItem(
                modifier = Modifier
                    .fillMaxWidth(),
                uiState = uiState,
                onEvent = onEvent,
                navController = navController
            )

            Spacer(modifier = Modifier.size(IBSpacing.spacingS))

            val securitySettingResDMO = uiState.settingSecureInfoDMO.securitySettingResDMO
            val smartOtpSetting = securitySettingResDMO?.smartOtpSetting

            if (smartOtpSetting.isNotNull() || securitySettingResDMO.isNull()) {
                ManageSmartOtp(
                    modifier = Modifier
                        .fillMaxWidth(),
                    uiState = uiState,
                    onEvent = onEvent,
                    navController = navController
                )

                Spacer(modifier = Modifier.size(IBSpacing.spacingS))
            }

            if (uiState.settingSecureInfoDMO.isRoleAdmin) {
                ManageSecureQuestion(
                    modifier = Modifier
                        .fillMaxWidth(),
                    uiState = uiState,
                    onEvent = onEvent,
                    navController = navController
                )
            }
        }
    }
}

@Composable
private fun ManageSecureQuestion(
    modifier: Modifier,
    uiState: SettingSecureInfoViewState,
    onEvent: (SettingSecureInfoViewEvent) -> Unit,
    navController: NavHostController,
) {
    val manageSecureQuestionConfig = ModelSettingSecureInfoConfigUI(
        title = stringResource(RLocalization.string.quan_ly_cau_hoi_bao_mat),
        infoItems = listOf(
            InfoItem(
                leadingIcon = RDesignSystem.drawable.quan_ly_cau_hoi_bao_mat_outline,
                content = stringResource(RLocalization.string.quan_ly_cau_hoi_bao_mat),
                thumbContentType = ThumbContentType.Icon,
                onClick = {
                    HomePageNavigationHelper.navigateToManageQuestionsScreen(navController)
                }
            )
        )
    )

    if (uiState.settingSecureInfoDMO.isRoleAdmin) {
        ManageSettingItems(
            modifier = modifier,
            config = manageSecureQuestionConfig,
            errorMessage = uiState.errorMessage,
            onEvent = onEvent
        )
    }
}

@Composable
private fun ManageSmartOtp(
    modifier: Modifier,
    uiState: SettingSecureInfoViewState,
    onEvent: (SettingSecureInfoViewEvent) -> Unit,
    navController: NavHostController,
) {
    val smartOtpSetting: SmartOtpSettingDMO? =
        uiState.settingSecureInfoDMO.securitySettingResDMO?.smartOtpSetting
    val statusSmartOtpThumbContent: ThumbContentType? =
        getTypeBadgeStatusSmartOtp(smartOtpSetting?.status?.uppercase())

    val infoItems: List<InfoItem> = mutableListOf<InfoItem>().apply {
        statusSmartOtpThumbContent?.let {
            add(
                InfoItem(
                    leadingIcon = RDesignSystem.drawable.trang_thai_smart_otp_outline,
                    content = stringResource(RLocalization.string.trang_thai_smart_otp),
                    thumbContentType = it,
                    onClick = {}
                )
            )
        }

        smartOtpSetting?.let { setting ->
            if (setting.status in listOf(StatusSmartOtp.ACTIVE.name, StatusSmartOtp.INACTIVE.name)) {
                add(
                    InfoItem(
                        leadingIcon = RDesignSystem.drawable.thiet_bi_dang_ky_smart_otp_outline,
                        content = stringResource(RLocalization.string.thiet_bi_dang_ky_smart_otp),
                        thumbContentType = setting.deviceModel?.let {
                            ThumbContentType.Badge(
                                text = it,
                                badgeColor = LabelColor.BRAND,
                                badgeType = LabelType.ROUNDED
                            )
                        },
                        onClick = {}
                    )
                )
            }
        }

        if (!uiState.settingSecureInfoDMO.isRoleAdmin && smartOtpSetting?.status in listOf(
                StatusSmartOtp.ACTIVE.name,
                StatusSmartOtp.INACTIVE.name
            )
        ) {
            add(
                InfoItem(
                    leadingIcon = RDesignSystem.drawable.kich_hoat_lai_smart_otp_outline,
                    content = stringResource(RLocalization.string.kich_hoat_lai_smart_otp),
                    thumbContentType = ThumbContentType.Icon,
                    onClick = {
                        HomePageNavigationHelper.navigateToReActiveSmartOtpScreen(navController)
                    }
                )
            )
        }

        if (smartOtpSetting?.status == StatusSmartOtp.ACTIVE.name && smartOtpSetting.deviceIdActiveSmartOTP == true) {
            add(
                InfoItem(
                    leadingIcon = RDesignSystem.drawable.doi_pin_smart_otp_outline,
                    content = stringResource(RLocalization.string.doi_pin_smart_otp),
                    thumbContentType = ThumbContentType.Icon,
                    onClick = {
                        HomePageNavigationHelper.navigateToChangePinScreen(navController)
                    }
                )
            )
        }
    }

    val manageSmartOtpConfig = ModelSettingSecureInfoConfigUI(
        title = stringResource(RLocalization.string.quan_ly_smart_otp),
        infoItems = infoItems,
    )

    ManageSettingItems(
        modifier = modifier,
        config = manageSmartOtpConfig,
        errorMessage = uiState.errorMessage,
        onEvent = onEvent
    )
}

@Composable
private fun ManageSignInItem(
    modifier: Modifier,
    uiState: SettingSecureInfoViewState,
    onEvent: (SettingSecureInfoViewEvent) -> Unit,
    navController: NavHostController,
) {
    val manageSignInConfig = ModelSettingSecureInfoConfigUI(
        title = stringResource(RLocalization.string.quan_ly_dang_nhap),
        infoItems = mutableListOf(
            InfoItem(
                leadingIcon = RDesignSystem.drawable.thay_doi_mat_khau_dang_nhap_outline,
                content = stringResource(RLocalization.string.thay_doi_mat_khau_dang_nhap),
                thumbContentType = ThumbContentType.Icon,
                onClick = {
                    HomePageNavigationHelper.navigateToPositiveChangePwScreen(navController)
                }
            )
        ).also {
            if (uiState.settingSecureInfoDMO.isShowConfigBiometric) {
                it.add(0, InfoItem(
                    leadingIcon = RDesignSystem.drawable.dang_nhap_bang_touch_id_outline,
                    content = stringResource(RLocalization.string.dang_nhap_bang_touch_id),
                    thumbContentType = ThumbContentType.Switch(
                        isChecked = uiState.settingSecureInfoDMO.isBiometricEnable,
                        onSwitchChanged = {
                            HomePageNavigationHelper.navigateToTurnOnBiometric(
                                navController,
                                it
                            )
                        }),
                    onClick = {}
                ))
            }
        }
    )

    ManageSettingItems(
        modifier = modifier,
        config = manageSignInConfig,
        errorMessage = uiState.errorMessage,
        onEvent = onEvent
    )

}

@Composable
private fun ManageSettingItems(
    modifier: Modifier,
    config: ModelSettingSecureInfoConfigUI,
    errorMessage: String?,
    onEvent: (SettingSecureInfoViewEvent) -> Unit,
) {
    Column(modifier = modifier) {
        Text(
            text = config.title,
            style = LocalTypography.current.titleTitle_s,
            color = LocalColorScheme.current.contentMainPrimary,
        )

        Spacer(Modifier.size(IBSpacing.spacingXs))

        if (config.infoItems.isEmpty()) {
            IBankEmptyState(
                modifier = Modifier.fillMaxWidth(),
                leadingIconButton = ImageVector.vectorResource(id = RDesignSystem.drawable.refresh_outline),
                supportingText = errorMessage,
                textButton = stringResource(id = R.string.retry),
                onClickButton = {
                    onEvent(SettingSecureInfoViewEvent.OnRefreshGetSetting)
                }
            )
        } else {
            Column (
                modifier = Modifier
                    .clip(RoundedCornerShape(IBSpacing.spacingS))
                    .background(Color.White)
            ) {
                config.infoItems.forEachIndexed { index, infoItem ->
                    SubMenuItem(
                        leadingIcon = infoItem.leadingIcon,
                        leadingModifier = Modifier.size(IBSpacing.spacing2xl),
                        content = infoItem.content,
                        styleContent = LocalTypography.current.bodyBody_l,
                        colorContent = LocalColorScheme.current.contentMainPrimary,
                        showDivider = index != config.infoItems.size - 1,
                        onClick = infoItem.onClick,
                        thumbContent = {
                            when (infoItem.thumbContentType) {
                                is ThumbContentType.Switch -> {
                                    Box(
                                        Modifier
                                            .padding(horizontal = IBSpacing.spacingM)
                                            .size(IBSpacing.spacingS)
                                    ) {
                                        IBankSwitch(
                                            modifier = Modifier
                                                .scale(1.2f),
                                            checked = infoItem.thumbContentType.isChecked,
                                            onCheckedChange = {
                                                infoItem.thumbContentType.onSwitchChanged(
                                                    it
                                                )
                                            }
                                        )
                                    }
                                }

                                is ThumbContentType.Badge -> {
                                    IBankBadgeLabel(
                                        modifier = Modifier,
                                        title = infoItem.thumbContentType.text,
                                        badgeSize = LabelSize.SM,
                                        badgeColor = infoItem.thumbContentType.badgeColor,
                                        badgeType = infoItem.thumbContentType.badgeType
                                    )
                                }

                                is ThumbContentType.Icon -> {
                                    Icon(
                                        painter = painterResource(RDesignSystem.drawable.arrow_right_outline),
                                        contentDescription = null,
                                        modifier = Modifier.size(IBSpacing.spacingL),
                                        tint = Color.Unspecified
                                    )
                                }

                                else -> {}
                            }
                        }
                    )

                }
            }
        }
    }
}

enum class StatusSmartOtp {
    WAITING_ACTIVATION, ACTIVE, INACTIVE, CLOSE, ERROR, WAITING_CONVERT
}

@Composable
private fun getTypeBadgeStatusSmartOtp(status: String?): ThumbContentType? {
    return when (status) {
        StatusSmartOtp.WAITING_ACTIVATION.name -> ThumbContentType.Badge(
            text = stringResource(RLocalization.string.cho_kich_hoat),
            badgeColor = LabelColor.INFO,
            badgeType = LabelType.ROUNDED
        )

        StatusSmartOtp.ACTIVE.name -> ThumbContentType.Badge(
            text = stringResource(RLocalization.string.hoat_dong),
            badgeColor = LabelColor.SUCCESS,
            badgeType = LabelType.ROUNDED
        )

        StatusSmartOtp.INACTIVE.name -> ThumbContentType.Badge(
            text = stringResource(RLocalization.string.tam_ngung),
            badgeColor = LabelColor.LIGHT_GRAY,
            badgeType = LabelType.ROUNDED
        )

        StatusSmartOtp.CLOSE.name, StatusSmartOtp.ERROR.name -> ThumbContentType.Badge(
            text = stringResource(RLocalization.string.khoa_tam_thoi),
            badgeColor = LabelColor.ERROR,
            badgeType = LabelType.ROUNDED
        )

        StatusSmartOtp.WAITING_CONVERT.name -> ThumbContentType.Badge(
            text = stringResource(RLocalization.string.cho_chuyen_doi),
            badgeColor = LabelColor.LIGHT_GRAY,
            badgeType = LabelType.ROUNDED
        )

        else -> {
            null
        }
    }
}