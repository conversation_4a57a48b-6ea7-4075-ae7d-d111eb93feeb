package vn.com.bidv.feature.homepage.ui.service

import IBGradient
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.common.extenstion.isNotNullOrEmpty
import vn.com.bidv.designsystem.component.EmptyStateType
import vn.com.bidv.designsystem.component.IBankEmptyState
import vn.com.bidv.designsystem.component.dataentry.IBankInputSearchBase
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.common.domain.data.PermissionResDMO
import vn.com.bidv.feature.homepage.constants.HomePageConstants
import vn.com.bidv.feature.homepage.navigation.HomepageRoute
import vn.com.bidv.feature.homepage.ui.service.ServiceReducer.ServiceViewEvent
import vn.com.bidv.feature.homepage.ui.service.ServiceReducer.ServiceViewState
import vn.com.bidv.feature.homepage.ui.service.modelUI.PermissionResUI
import vn.com.bidv.feature.homepage.ui.service.modelUI.toPresenterModel
import vn.com.bidv.sdkbase.ui.HandleBackAction
import vn.com.bidv.localization.R as localizationR

@Composable
fun ServiceScreen(navController: NavHostController, showBottomBar: MutableState<Boolean>) {
    val serviceViewModel: ServiceViewModel = hiltViewModel()
    BaseScreen(
        navController = navController,
        viewModel = serviceViewModel,
        isLightStatusBar = false,
        renderContent = {uiState, onEvent ->
            if (!uiState.isInitSuccess) {
                onEvent(ServiceViewEvent.OnInitDataService)
            }
            ServiceContent(
                uiState = uiState,
                onEvent = onEvent,
                navigation = navController,
                showBottomBar = showBottomBar
            )
        },
        topAppBarConfig = TopAppBarConfig(isShowTopAppBar = false),
    )
}

@Composable
private fun ServiceContent(
    uiState: ServiceViewState,
    onEvent: (ServiceViewEvent) -> Unit,
    navigation: NavHostController,
    showBottomBar: MutableState<Boolean>
) {
    val focusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current
    val colorSchema = LocalColorScheme.current

    LaunchedEffect(uiState.isSearchView) {
        showBottomBar.value = !uiState.isSearchView
        if (!uiState.isSearchView) {
            focusManager.clearFocus()
        }
    }

    HandleBackAction(
        isEnabled = uiState.isSearchView,
        action = {
            onEvent(ServiceViewEvent.SetSearchView(false))
        }
    )

    Box(modifier = Modifier.fillMaxSize().background(IBGradient.bg_topnav)) {
        Column(
            modifier = Modifier.fillMaxSize().background(Color.Transparent)
        ) {

            Box(modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .windowInsetsPadding(WindowInsets.statusBars)
                .padding(
                    top = 29.dp, start = IBSpacing.spacingM, end = IBSpacing.spacingM
                ),) {

                val inputPadding by animateDpAsState(
                    targetValue = if (uiState.isSearchView) IBSpacing.spacing4xl else IBSpacing.spacingNone,
                    animationSpec = tween(durationMillis = 300)
                )

                if (uiState.isSearchView) {
                    IconButton(
                        modifier = Modifier.size(IBSpacing.spacing3xl, IBSpacing.spacing3xl).align(Alignment.CenterStart),
                        onClick = { onEvent(ServiceViewEvent.SetSearchView(false)) }) {
                        Icon(
                            modifier = Modifier.defaultMinSize(IBSpacing.spacingL, IBSpacing.spacingL),
                            painter = painterResource(vn.com.bidv.designsystem.R.drawable.arrow_left_outline),
                            contentDescription = null,
                            tint = Color.White
                        )
                    }
                }

                IBankInputSearchBase(
                    modifier = Modifier
                        .fillMaxWidth().padding(start =  inputPadding, bottom = IBSpacing.spacingXs).focusRequester(focusRequester),
                    placeHolderText = stringResource(localizationR.string.tim_kiem),
                    textValue = uiState.textSearch,
                    onTextChange = { onEvent(ServiceViewEvent.UpdateSearchText(it)) },
                    onRequestChange = { txtSearch ->
                    },
                    maxLength = HomePageConstants.MAX_LENGTH_SEARCH_SERVICE,
                    onFocusChange = { isFocus ->
                        if (isFocus) {
                            onEvent(ServiceViewEvent.SetSearchView(true))
                        } else {
                            if (uiState.textSearch.isEmpty()) {
                                onEvent(ServiceViewEvent.SetSearchView(false))
                            }
                        }
                    },
                )
            }

            Box(modifier = Modifier.fillMaxSize().background(
                if (!uiState.isSearchView)
                    colorSchema.bgMainTertiary
                else
                    colorSchema.bgMainPrimary
            )) {
                if (!uiState.isSearchView) {
                    if (uiState.listService.isNotEmpty()) {
                        LazyVerticalGrid(
                            columns = GridCells.Fixed(3),
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(
                                    IBSpacing.spacingM
                                ),
                            verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingM),
                            horizontalArrangement = Arrangement.spacedBy(IBSpacing.spacingM),
                        ) {
                            items(uiState.listService.size) { index ->
                                MenuItemService(
                                    permissionResDMO = uiState.listService[index],
                                    navigation = navigation,
                                    onClickItem = {
                                        val data = uiState.listService[index]
                                        if (data.toPresenterModel().routeID.isNotNullOrEmpty()) {
                                            navigation.navigate(data.toPresenterModel().routeID.orEmpty())
                                        } else {
                                            navigation.navigate(
                                                HomepageRoute.ListServiceRoute(
                                                    permissionResDMO = data
                                                )
                                            )
                                        }
                                    }
                                )
                            }
                        }
                    } else {
                        IBankEmptyState(
                            modifier = Modifier
                                .fillMaxSize(),
                            backgroundColor = Color.Transparent,
                            emptyStateType = EmptyStateType.SearchNoResult,
                            supportingText = stringResource(vn.com.bidv.localization.R.string.khong_co_du_lieu),
                        )
                    }
                } else {
                    if (uiState.listSearchChildrenService.isNotEmpty()) {
                        LazyColumn(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(
                                    IBSpacing.spacingM,
                                ),
                            verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingS)
                        ) {
                            items(uiState.listSearchChildrenService.size) { index ->
                                MenuItemChildServiceListView(uiState.listSearchChildrenService[index].children.orEmpty() ,
                                    stringResource(uiState.listSearchChildrenService[index].toPresenterModel().title),
                                    navigation = navigation
                                )
                            }

                            item {
                                Box(
                                    modifier = Modifier.height(90.dp)
                                )
                            }
                        }
                    } else {
                        if (uiState.textSearch.isNotEmpty()) {
                            IBankEmptyState(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(bottom = IBSpacing.spacing5xl),
                                backgroundColor = Color.Transparent,
                                emptyStateType = EmptyStateType.SearchNoResult,
                                title = stringResource(vn.com.bidv.localization.R.string.khong_co_ket_qua_phu_hop),
                                supportingText = stringResource(vn.com.bidv.localization.R.string.hay_thu_lai_voi_tu_khoa_khac),
                            )
                        } else {
                            IBankEmptyState(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(bottom = IBSpacing.spacing5xl),
                                backgroundColor = Color.Transparent,
                                emptyStateType = EmptyStateType.SearchNoResult,
                                supportingText = stringResource(vn.com.bidv.localization.R.string.nhap_tu_khoa_de_tim_kiem),
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun MenuItemService(
    permissionResDMO: PermissionResDMO,
    navigation: NavHostController,
    colorBoxItem: Color = LocalColorScheme.current.bgMainPrimary,
    onClickItem: () -> Unit = { }
) {
    val colorSchema = LocalColorScheme.current
    val typography = LocalTypography.current
    Column(
        modifier = Modifier.fillMaxWidth().clickable {
            onClickItem()
        },
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingXs)
    ) {

        Box(
            modifier = Modifier
                .size(IBSpacing.spacing4xl)
                .clip(CircleShape)
                .background(colorBoxItem),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                modifier = Modifier.size(IBSpacing.spacing2xl),
                imageVector = ImageVector.vectorResource(id = permissionResDMO.toPresenterModel().icon),
                contentDescription = null,
                tint = Color.Unspecified,
            )
        }

        Text(
            text = stringResource(permissionResDMO.toPresenterModel().title),
            color = colorSchema.contentMainSecondary,
            style = typography.bodyBody_s,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
fun MenuItemChildServiceListView(
    datas: List<PermissionResDMO>,
    title: String,
    navigation: NavHostController,
) {
    val colorSchema = LocalColorScheme.current
    val typography = LocalTypography.current
    Column(
        verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingL),
        modifier = Modifier
            .fillMaxWidth()
            .background(
        color = colorSchema.bgMainTertiary,
        shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL),
         )
            .padding(IBSpacing.spacingM)) {
        if (title.isNotEmpty()) {
            Text(
                text = title,
                color = colorSchema.contentMainPrimary,
                style = typography.titleTitle_m
            )
        }

        Column(
            modifier = Modifier.fillMaxWidth()
        ) {
            datas.forEach { data ->
                MenuItemChildServiceListItem(
                    data.toPresenterModel(),
                    onClick = {
                        if (data.toPresenterModel().routeID.isNotNullOrEmpty()) {
                            navigation.navigate(data.toPresenterModel().routeID.orEmpty())
                        } else {
                            navigation.navigate(
                                HomepageRoute.ListServiceRoute(
                                    permissionResDMO = data
                                )
                            )
                        }
                    }
                )
            }
        }
    }
}

@Composable
fun MenuItemChildServiceListItem(
    data: PermissionResUI,
    onClick: () -> Unit = {}
) {
    val colorSchema = LocalColorScheme.current
    val typography = LocalTypography.current
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = ripple(
                    bounded = true,
                    color = LocalColorScheme.current.bgSolidPrimary_press,
                ),
                onClick = onClick
            )
            .padding(vertical = IBSpacing.spacingS),
    verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            modifier = Modifier.size(IBSpacing.spacing2xl),
            imageVector = ImageVector.vectorResource(id = data.icon),
            contentDescription = null,
            tint = Color.Unspecified,
        )

        Text(
            text = stringResource(data.title),
            color = colorSchema.contentMainPrimary,
            style = typography.bodyBody_l,
            modifier = Modifier.padding(start = IBSpacing.spacingS)
        )

        Spacer(modifier = Modifier.weight(1f))

        Icon(
            modifier = Modifier.size(IBSpacing.spacingM),
            imageVector = ImageVector.vectorResource(id = vn.com.bidv.designsystem.R.drawable.arrow_right_outline ),
            contentDescription = null,
            tint = Color.Unspecified,
        )

    }
}
