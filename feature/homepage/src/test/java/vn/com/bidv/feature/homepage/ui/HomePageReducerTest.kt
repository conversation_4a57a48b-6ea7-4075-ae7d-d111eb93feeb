package vn.com.bidv.feature.homepage.ui

import org.junit.Assert.assertEquals
import org.junit.Test
import vn.com.bidv.feature.homepage.ui.homepage.HomePageReducer

class HomePageReducerTest {

    private val reducer = HomePageReducer()

    @Test
    fun `test OnClickItemBottomBar event`() {
        val initialState = HomePageReducer.HomePageViewState()
        val event = HomePageReducer.HomePageViewEvent.OnClickItemBottomBar(2)

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState.copy(selectedIndex = 2), newState)
        assertEquals(null, effect)
    }

    @Test
    fun `test MarkTotalNotifyAsShouldFetch event`() {
        val initialState = HomePageReducer.HomePageViewState()
        val event = HomePageReducer.HomePageViewEvent.MarkTotalNotifyAsShouldFetch(false)

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState.copy(shouldFetchTotalNotifyUnread = false), newState)
        assertEquals(null, effect)
    }
}