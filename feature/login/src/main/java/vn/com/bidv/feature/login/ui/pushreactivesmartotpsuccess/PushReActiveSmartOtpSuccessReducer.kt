package vn.com.bidv.feature.login.ui.pushreactivesmartotpsuccess

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.login.domain.model.SmartOtpApprovePendingDMO
import vn.com.bidv.feature.login.ui.pushreactivesmartotpsuccess.model.ModelPushReActiveSmartOtpSuccessUI

class PushReActiveSmartOtpSuccessReducer :
    Reducer<PushReActiveSmartOtpSuccessReducer.PushReActiveSmartOtpSuccessViewState, PushReActiveSmartOtpSuccessReducer.PushReActiveSmartOtpSuccessViewEvent, PushReActiveSmartOtpSuccessReducer.PushReActiveSmartOtpSuccessViewEffect> {

    @Immutable
    data class PushReActiveSmartOtpSuccessViewState(
        val isInitSuccess: Boolean = false,
        val model: SmartOtpApprovePendingDMO = SmartOtpApprovePendingDMO()
    ) : ViewState

    @Immutable
    sealed class PushReActiveSmartOtpSuccessViewEvent : ViewEvent {
        data class OnInitScreen(val modeUI: SmartOtpApprovePendingDMO) :
            PushReActiveSmartOtpSuccessViewEvent()
    }

    @Immutable
    sealed class PushReActiveSmartOtpSuccessViewEffect : SideEffect {

    }

    override fun reduce(
        previousState: PushReActiveSmartOtpSuccessViewState,
        event: PushReActiveSmartOtpSuccessViewEvent,
    ): Pair<PushReActiveSmartOtpSuccessViewState, PushReActiveSmartOtpSuccessViewEffect?> {
        return handelPushReActiveSmartOtpSuccessEvent(previousState, event)
    }

    private fun handelPushReActiveSmartOtpSuccessEvent(
        previousState: PushReActiveSmartOtpSuccessViewState,
        event: PushReActiveSmartOtpSuccessViewEvent
    ): Pair<PushReActiveSmartOtpSuccessViewState, PushReActiveSmartOtpSuccessViewEffect?> {
        return when (event) {
            is PushReActiveSmartOtpSuccessViewEvent.OnInitScreen -> {
                previousState.copy(
                    isInitSuccess = true,
                    model = event.modeUI
                ) to null
            }
        }
    }
}
