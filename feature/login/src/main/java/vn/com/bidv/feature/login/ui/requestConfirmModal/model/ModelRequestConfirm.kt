package vn.com.bidv.feature.login.ui.setupsecurityinfo.requestConfirmModal.model

import com.google.gson.annotations.SerializedName
import kotlinx.serialization.Serializable
import vn.com.bidv.feature.login.domain.model.ModelLoginDMO
import vn.com.bidv.feature.login.domain.model.SecurityQuestionAnswerDMO

@Serializable
data class ModelRequestConfirm(
    @SerializedName("modelLoginDMO")
    val modelLoginDMO: ModelLoginDMO? = null,
    @SerializedName("listQuestion")
    var listQuestion: List<SecurityQuestionAnswerDMO> = emptyList(),
)
