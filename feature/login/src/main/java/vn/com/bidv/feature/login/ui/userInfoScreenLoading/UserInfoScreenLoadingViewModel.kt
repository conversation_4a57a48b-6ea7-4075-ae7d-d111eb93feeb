package vn.com.bidv.feature.login.ui.userInfoScreenLoading

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.feature.common.di.AuthProvider
import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.feature.common.ui.component.callDomainLogout
import vn.com.bidv.feature.login.domain.LoginUseCase
import vn.com.bidv.feature.login.ui.userInfoScreenLoading.UserInfoScreenLoadingReducer.UserInfoScreenLoadingViewEffect
import vn.com.bidv.feature.login.ui.userInfoScreenLoading.UserInfoScreenLoadingReducer.UserInfoScreenLoadingViewEvent
import vn.com.bidv.feature.login.ui.userInfoScreenLoading.UserInfoScreenLoadingReducer.UserInfoScreenLoadingViewState
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class UserInfoScreenLoadingViewModel @Inject constructor(
    private val loginUseCase: LoginUseCase,
    private val authProvider: AuthProvider,
    private val userInfoUseCase: UserInfoUseCase,
) : ViewModelIBankBase<UserInfoScreenLoadingViewState, UserInfoScreenLoadingViewEvent, UserInfoScreenLoadingViewEffect>(
    initialState = UserInfoScreenLoadingViewState(),
    reducer = UserInfoScreenLoadingReducer()
) {
    override fun handleEffect(
        sideEffect: UserInfoScreenLoadingViewEffect,
        onResult: (UserInfoScreenLoadingViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is UserInfoScreenLoadingViewEffect.OnGetUserInfoSideEffect -> {
                callDomain(
                    isListenAllError = true,
                    isListenSessionExpired = true,
                    onSuccess = {
                        it.data?.let { userInfoData ->
                            userInfoUseCase.saveUserInfoToStorage(userInfoData)
                        }
                        onResult(
                            UserInfoScreenLoadingViewEvent.OnGetUserInfoSuccess(it.data)
                        )
                    },
                    onFail = {
                        onResult(
                            UserInfoScreenLoadingViewEvent.OnGetUserInfoFail(it?.errorMessage.orEmpty())
                        )
                    }
                ) {
                    loginUseCase.getUserInfo()
                }
            }

            is UserInfoScreenLoadingViewEffect.OnLogoutSideEffect -> {
                callDomainLogout(
                    authProvider = authProvider,
                    onResult = {
                        onResult(
                            UserInfoScreenLoadingViewEvent.OnLogoutSuccess
                        )
                    }
                )
            }

            else -> {
                //Do nothing
            }
        }
    }
}
