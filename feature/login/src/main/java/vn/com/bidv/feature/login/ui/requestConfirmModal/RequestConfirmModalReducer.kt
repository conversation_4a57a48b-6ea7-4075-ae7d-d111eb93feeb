package vn.com.bidv.feature.login.ui.requestConfirmModal

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState

class RequestConfirmModalReducer :
    Reducer<RequestConfirmModalReducer.RequestConfirmModalViewState, RequestConfirmModalReducer.RequestConfirmModalViewEvent, RequestConfirmModalReducer.RequestConfirmModalViewEffect> {

    data object RequestConfirmModalViewState : ViewState

    @Immutable
    sealed class RequestConfirmModalViewEvent : ViewEvent {}

    @Immutable
    sealed class RequestConfirmModalViewEffect : SideEffect {}

    override fun reduce(
        previousState: RequestConfirmModalViewState,
        event: RequestConfirmModalViewEvent,
    ): Pair<RequestConfirmModalViewState, RequestConfirmModalViewEffect?> {
        return previousState to null
    }
}
