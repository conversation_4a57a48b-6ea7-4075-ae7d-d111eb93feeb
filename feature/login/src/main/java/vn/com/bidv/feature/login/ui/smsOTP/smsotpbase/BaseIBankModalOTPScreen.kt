package vn.com.bidv.feature.login.ui.smsOTP.smsotpbase

import android.app.Activity
import android.content.Context
import android.content.Context.RECEIVER_EXPORTED
import android.content.ContextWrapper
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.SnackbarDuration
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.fragment.app.FragmentActivity
import com.google.android.gms.auth.api.phone.SmsRetriever
import kotlinx.coroutines.delay
import timber.log.Timber
import vn.com.bidv.common.extenstion.isNotNull
import vn.com.bidv.common.extenstion.isNotNullOrEmpty
import vn.com.bidv.common.extenstion.isNull
import vn.com.bidv.designsystem.component.feedback.otpview.IBankOtpItemInputList
import vn.com.bidv.designsystem.component.feedback.snackbar.IBankSnackBar
import vn.com.bidv.designsystem.component.keyboard.NumpadKeyboard
import vn.com.bidv.designsystem.component.navigation.button.IBankLinkButton
import vn.com.bidv.designsystem.component.navigation.button.LinkButtonSize
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.feature.login.domain.model.IIBankModelOTPDMO
import vn.com.bidv.feature.login.ui.smsOTP.smsreceiver.SMSReceiver
import vn.com.bidv.feature.login.ui.smsOTP.smsreceiver.SMSReceiverListener
import vn.com.bidv.log.BLogUtil
import java.util.regex.Pattern
import kotlin.math.ceil
import vn.com.bidv.localization.R as RLocalization

/**
 *
 * @param navController NavHostController
 * @param I is input from previous screen
 * @param R is output to resend otp
 * @param T is output to verify otp
 *
 */
@Composable
fun <I : IIBankModelOTPDMO, R : IIBankModelOTPDMO, T> BaseIBankModalOTPContent(
    uiState: BaseIBankModalOTPReducer.BaseIBankModalOTPViewState<I, R, T>,
    onEvent: (BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent<I, R, T>) -> Unit,
    viewModel: BaseIBankModalOTPViewModel<I, R, T>
) {

    if (!uiState.initSuccess) return

    val otpLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK && result.data.isNotNull()) {
            val message = result.data?.getStringExtra(SmsRetriever.EXTRA_SMS_MESSAGE)
            BLogUtil.d("message: $message")
            val otp = message?.getOtpFromSMS()
            if (otp.isNotNullOrEmpty()) {
                onEvent(
                    BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnFillOTPFromSMS(
                        otp ?: ""
                    )
                )
            }
        }
    }

    val activity = findFragmentActivity()

    RegisterSMSBroadcastReceiver(otpLauncher = otpLauncher, activity = activity, viewModel = viewModel)

    var timeoutResendOTPObs by remember { mutableLongStateOf(ceil((uiState.timeoutResendOTP - System.currentTimeMillis()).toDouble() / 1000).toLong()) }
    var timeoutEffectOTPObs by remember { mutableLongStateOf((uiState.timeoutEffectOTP - System.currentTimeMillis()) / 1000) }
    var retrySuccessNumber by remember { mutableIntStateOf(0) }

    LaunchedEffect(uiState.retryOTPSuccessNumber) {
        while (true) {
            timeoutResendOTPObs = ceil((uiState.timeoutResendOTP - System.currentTimeMillis()).toDouble() / 1000).toLong()
            timeoutEffectOTPObs = ceil((uiState.timeoutEffectOTP - System.currentTimeMillis()).toDouble() / 1000).toLong()
            delay(1000)
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Transparent)
    ) {

        val colorScheme = LocalColorScheme.current
        val typography = LocalTypography.current

        Column(Modifier.fillMaxSize()) {
            Box(
                modifier = Modifier.weight(1f),
                contentAlignment = Alignment.Center
            ) {
                Surface(
                    shape = RoundedCornerShape(IBSpacing.spacingL),
                    color = colorScheme.contentOn_specialPrimary,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = IBSpacing.spacingL, end = IBSpacing.spacingL),
                ) {
                    Box {
                        Column(
                            modifier = Modifier
                                .wrapContentSize()
                                .padding(
                                    start = IBSpacing.spacingL,
                                    top = IBSpacing.spacingM,
                                    bottom = IBSpacing.spacingL,
                                    end = IBSpacing.spacingM
                                ),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {

                            Spacer(modifier = Modifier.height(IBSpacing.spacingS))

                            Text(
                                text = stringResource(RLocalization.string.xac_thuc),
                                style = typography.titleTitle_l,
                                modifier = Modifier
                                    .padding(horizontal = IBSpacing.spacing2xl),
                                textAlign = TextAlign.Center
                            )

                            Spacer(modifier = Modifier.height(IBSpacing.spacingXs))

                            Text(
                                text = uiState.reSendOTPData?.getContent()
                                    ?: uiState.inputData?.getContent()
                                        ?.takeIf { it.isNotNullOrEmpty() }
                                    ?: stringResource(vn.com.bidv.localization.R.string.vui_long_nhap_ma_otp_da_duoc_gui_den_s_de_xac_thuc_kich_hoat_tai_khoan).format(
                                        uiState.reSendOTPData?.getMaskedPhoneOrEmailOTP()
                                            ?: uiState.inputData?.getMaskedPhoneOrEmailOTP()
                                            ?: ""
                                    ),
                                style = typography.bodyBody_m,
                                textAlign = TextAlign.Center
                            )

                            Spacer(modifier = Modifier.height(IBSpacing.spacingM))

                            IBankOtpItemInputList(
                                modifier = Modifier.fillMaxWidth(),
                                otpItemNumber = 6,
                                otpText = uiState.otp,
                                isFocus = true,
                            ) {
                                if (timeoutEffectOTPObs > 0) {
                                    onEvent(BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnVerifyOTP)
                                }
                            }

                            if (timeoutEffectOTPObs <= 0) {
                                Text(
                                    modifier = Modifier
                                        .padding(top = IBSpacing.spacingXs),
                                    text = stringResource(RLocalization.string.ma_otp_da_het_thoi_gian_hieu_luc),
                                    style = typography.captionCaption_m,
                                    color = colorScheme.contentNegativePrimary,
                                    textAlign = TextAlign.Center
                                )
                            }

                            Spacer(modifier = Modifier.height(IBSpacing.spacingS))

                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .defaultMinSize(minHeight = 58.dp),
                                horizontalArrangement = Arrangement.Center,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    modifier = Modifier.wrapContentWidth(),
                                    text = stringResource(RLocalization.string.khong_nhan_duoc_otp),
                                    style = LocalTypography.current.bodyBody_m,
                                    color = LocalColorScheme.current.contentMainTertiary,
                                )
                                Spacer(modifier = Modifier.size(IBSpacing.spacing2xs))

                                if (timeoutResendOTPObs > 0) {
                                    Text(
                                        modifier = Modifier.wrapContentWidth(),
                                        text = stringResource(
                                            RLocalization.string.gui_lai_sau_ss,
                                            timeoutResendOTPObs.toString()
                                        ),
                                        style = LocalTypography.current.bodyBody_m.copy(fontWeight = FontWeight.W500),
                                        color = LocalColorScheme.current.contentMainTertiary,
                                    )
                                } else {
                                    IBankLinkButton(
                                        modifier = Modifier.wrapContentWidth(),
                                        size = LinkButtonSize.SM(
                                            LocalTypography.current,
                                            LocalColorScheme.current
                                        ),
                                        isEnable = true,
                                        text = stringResource(RLocalization.string.gui_lai_ma),
                                        onClick = {
                                            activity?.baseContext?.let { startSmsUserConsent(it) }
                                            onEvent(BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnRetrySendOTP)
                                        },
                                    )
                                }
                            }
                        }
                        IconButton(
                            modifier = Modifier.align(Alignment.TopEnd),
                            onClick = {
                                onEvent(BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnDismiss)
                            }
                        ) {
                            Icon(
                                painter = painterResource(vn.com.bidv.designsystem.R.drawable.modal_confirm_close),
                                contentDescription = null
                            )
                        }
                    }
                }
            }
            Box {
                NumpadKeyboard(modifier = Modifier.align(Alignment.BottomCenter)) { key ->
                    onEvent(BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnOTPChanged(key))
                }
            }
        }

        if (retrySuccessNumber != uiState.retryOTPSuccessNumber) {
            Box(
                modifier = Modifier
                    .wrapContentSize()
                    .padding(start = IBSpacing.spacingM, end = IBSpacing.spacingM)
            ) {
                IBankSnackBar(
                    modifier = Modifier.align(Alignment.TopEnd),
                    message = stringResource(RLocalization.string.gui_lai_otp_thanh_cong),
                    secondaryButtonText = stringResource(RLocalization.string.dong),
                    snackbarDuration =  SnackbarDuration.Short,
                    onDismiss = {
                        retrySuccessNumber = uiState.retryOTPSuccessNumber
                    },
                    secondaryButtonAction = {
                        retrySuccessNumber = uiState.retryOTPSuccessNumber
                    }
                )
            }
        }
    }
}

@Composable
private fun findFragmentActivity(): FragmentActivity? {
    var context = LocalContext.current
    while (context is ContextWrapper) {
        if (context is FragmentActivity) {
            return context
        }
        context = context.baseContext
    }
    return null
}

@Composable
private fun <I : IIBankModelOTPDMO, R : IIBankModelOTPDMO, T> RegisterSMSBroadcastReceiver(
    otpLauncher: ManagedActivityResultLauncher<Intent, ActivityResult>,
    activity: FragmentActivity?,
    viewModel: BaseIBankModalOTPViewModel<I, R, T>
) {
    if (viewModel.smsReceiver.isNull()) {
        activity?.baseContext?.let { startSmsUserConsent(it) }
        viewModel.smsReceiver = SMSReceiver()
        viewModel.smsReceiver?.smsReceiverListener = object : SMSReceiverListener {
            override fun onReceiveMessage(smsInIntent: Intent?) {
                smsInIntent?.let {
                    otpLauncher.launch(it)
                }
            }
        }
    }
    DisposableEffect(Unit) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            activity?.registerReceiver(
                viewModel.smsReceiver,
                IntentFilter(SmsRetriever.SMS_RETRIEVED_ACTION),
                RECEIVER_EXPORTED
            )
        } else {
            activity?.registerReceiver(
                viewModel.smsReceiver,
                IntentFilter(SmsRetriever.SMS_RETRIEVED_ACTION)
            )
        }
        onDispose {
            unRegisterSMSBroadcastReceiver(activity, viewModel)
        }
    }
}

private fun <I : IIBankModelOTPDMO, R : IIBankModelOTPDMO, T> unRegisterSMSBroadcastReceiver(
    activity: FragmentActivity?,
    viewModel: BaseIBankModalOTPViewModel<I, R, T>
) {
    viewModel.smsReceiver?.let {
        try {
            activity?.unregisterReceiver(viewModel.smsReceiver)
        } catch (e: Exception) {
            BLogUtil.logException(e)
        }
    }
}

private fun startSmsUserConsent(context: Context) {
    SmsRetriever.getClient(context).startSmsUserConsent(null)
}

private fun String.getOtpFromSMS(): String? {
    Timber.d("Sms content: $this")
    val otpPattern = Pattern.compile("(|^)\\d{6}")
    var matcher = otpPattern.matcher(this)
    return if (matcher.find()) matcher.group(0) else ""
}
