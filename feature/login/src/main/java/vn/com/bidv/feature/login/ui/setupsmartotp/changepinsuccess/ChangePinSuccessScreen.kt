package vn.com.bidv.feature.login.ui.setupsmartotp.changepinsuccess

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.R.drawable
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankActionBar
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankTopAppBar
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.localization.R.string
import vn.com.bidv.sdkbase.navigation.IBankMainRouting

@Composable
fun ChangePinSuccessScreen(navController: NavHostController) {

    BackHandler {
        navController.navigate(IBankMainRouting.HomeRoute.HomeMainRoute.route) {
            popUpTo(IBankMainRouting.HomeRoute.HomeMainRoute.route) {
                inclusive = true
            }
        }
    }

    ChangePinSuccessContent(navController)
}

@Composable
private fun ChangePinSuccessContent(
    navController: NavHostController,
) {
    Scaffold(
        topBar = {
            IBankTopAppBar(
                navController = navController,
                topAppBarType = TopAppBarType.Result,
                topAppBarConfig = TopAppBarConfig(
                    isShowNavigationIcon = true,
                    iconLeading = drawable.bidv_direct_logo
                )
            )
        },
        contentWindowInsets = WindowInsets(0.dp, 0.dp, 0.dp, 0.dp),
        containerColor = LocalColorScheme.current.borderMainTertiary
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(it)
                .padding(WindowInsets.navigationBars.asPaddingValues())
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(IBSpacing.spacingM),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {

                Icon(
                    painter = painterResource(id = drawable.icon_popup_success_64),
                    contentDescription = null,
                    modifier = Modifier.size(64.dp),
                    tint = Color.Unspecified
                )

                Spacer(Modifier.size(IBSpacing.spacingXs))

                Text(
                    text = stringResource(string.doi_pin_thanh_cong),
                    style = LocalTypography.current.headlineHeadline_s,
                    color = LocalColorScheme.current.contentMainPrimary
                )

                Spacer(Modifier.size(IBSpacing.spacing2xs))


                Text(
                    modifier = Modifier
                        .fillMaxWidth(),
                    text = stringResource(string.pin_smart_otp_moi_cua_quy_khach_da_duoc_he_thong_cap_nhat_thanh_cong),
                    style = LocalTypography.current.bodyBody_m,
                    color = LocalColorScheme.current.contentMainTertiary,
                    textAlign = TextAlign.Center
                )

                Spacer(Modifier.size(IBSpacing.spacingM))

            }

            IBankActionBar(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .fillMaxWidth(),
                buttonPositive = DialogButtonInfo(
                    label = stringResource(string.quay_ve_trang_chu),
                    onClick = {
                        navController.navigate(IBankMainRouting.HomeRoute.HomeMainRoute.route) {
                            popUpTo(IBankMainRouting.HomeRoute.HomeMainRoute.route) {
                                inclusive = true
                            }
                        }
                    }
                )
            )
        }
    }

}
