package vn.com.bidv.feature.login.domain.model

import com.google.gson.annotations.SerializedName
import kotlinx.serialization.Serializable

@Serializable
data class AdminTransVerifyDMO(
    /* total record */
    @SerializedName("total")
    val total: Int? = null,

    /* total error */
    @SerializedName("totalError")
    val totalError: Int? = null,

    /* total Success */
    @SerializedName("totalSuccess")
    val totalSuccess: Int? = null,

    /* Y|N */
    @SerializedName("errors")
    val errors: List<TransRequestApprovalDMO>? = null,

    @SerializedName("actionType")
    val actionType: String? = null,

    @SerializedName("transInfo")
    val transInfo: TransRequestApprovalDMO? = null,
)
