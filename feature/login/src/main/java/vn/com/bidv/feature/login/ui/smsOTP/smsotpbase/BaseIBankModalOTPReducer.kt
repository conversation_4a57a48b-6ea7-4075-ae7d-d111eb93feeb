package vn.com.bidv.feature.login.ui.smsOTP.smsotpbase

import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.designsystem.component.keyboard.KeyInput
import vn.com.bidv.feature.login.domain.model.IIBankModelOTPDMO

/**
 *
 * @param I is input from previous screen
 * @param R is output to resend otp
 * @param T is output to verify otp
 *
 */

class BaseIBankModalOTPReducer<I : IIBankModelOTPDMO, R : IIBankModelOTPDMO, T> :
    Reducer<BaseIBankModalOTPReducer.BaseIBankModalOTPViewState<I, R, T>, BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent<I, R, T>, BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect<I, R, T>> {

    data class BaseIBankModalOTPViewState<out I, out R, out T>(
        val initSuccess: Boolean = false,
        val otp: String = "",
        val retryOTPSuccessNumber: Int = 0,
        val inputData: I? = null,
        val reSendOTPData: R? = null,
        val verifyOTPData: T? = null,
        val timeoutResendOTP: Long = 0,
        val timeoutEffectOTP: Long = 0
    ) : ViewState

    open class BaseIBankModalOTPViewEvent<out I, out R, out T> : ViewEvent {
        data class InitData<I>(val inputConfigData: I) :
            BaseIBankModalOTPViewEvent<I, Nothing, Nothing>()

        data class OnOTPChanged(val keyInput: KeyInput) :
            BaseIBankModalOTPViewEvent<Nothing, Nothing, Nothing>()

        data object OnRetrySendOTP : BaseIBankModalOTPViewEvent<Nothing, Nothing, Nothing>()
        data object OnVerifyOTP : BaseIBankModalOTPViewEvent<Nothing, Nothing, Nothing>()
        data class OnRetrySendOTPSuccess<R>(val data: R?) :
            BaseIBankModalOTPViewEvent<Nothing, R, Nothing>()

        data class OnVerifyOTPSuccess<T>(val data: T?) :
            BaseIBankModalOTPViewEvent<Nothing, Nothing, T>()

        data class OnVerifyOTPError(val errorCode: String?, val errorMessage: String?) :
            BaseIBankModalOTPViewEvent<Nothing, Nothing, Nothing>()

        data object ClearOTP : BaseIBankModalOTPViewEvent<Nothing, Nothing, Nothing>()

        data object OnDismiss : BaseIBankModalOTPViewEvent<Nothing, Nothing, Nothing>()

        data class OnFillOTPFromSMS(val otp: String) :
            BaseIBankModalOTPViewEvent<Nothing, Nothing, Nothing>()
    }

    open class BaseIBankModalOTPViewEffect<out I, out R, out T> : SideEffect {
        data class VerifyOTPError(val errorCode: String?, val errorMessage: String?) : BaseIBankModalOTPViewEffect<Nothing, Nothing, Nothing>(), UIEffect

        data class RetrySendOTP(
            val method: String,
            val userName: String,
            val txnId: String
        ) :
            BaseIBankModalOTPViewEffect<Nothing, Nothing, Nothing>()

        data class VerifyOTP(
            val method: String,
            val userName: String,
            val txnId: String,
            val otpNum: String,
        ) : BaseIBankModalOTPViewEffect<Nothing, Nothing, Nothing>()

        data object RetryOTPSuccess : BaseIBankModalOTPViewEffect<Nothing, Nothing, Nothing>(),
            UIEffect

        data class VerifyOTPSuccess<T>(val verifyOTPData: T?) :
            BaseIBankModalOTPViewEffect<Nothing, Nothing, T>(),
            UIEffect

        data object OnDismiss : BaseIBankModalOTPViewEffect<Nothing, Nothing, Nothing>(), UIEffect
    }

    override fun reduce(
        previousState: BaseIBankModalOTPViewState<I, R, T>,
        event: BaseIBankModalOTPViewEvent<I, R, T>,
    ): Pair<BaseIBankModalOTPViewState<I, R, T>, BaseIBankModalOTPViewEffect<I, R, T>?> {
        return when (event) {
            is BaseIBankModalOTPViewEvent.InitData -> {
                previousState.copy(
                    inputData = event.inputConfigData,
                    timeoutResendOTP = System.currentTimeMillis() + (event.inputConfigData.getRetryTimeSendOTP()?.toLongOrNull() ?: 0) * 1000,
                    timeoutEffectOTP = System.currentTimeMillis() + (event.inputConfigData.getActiveTimeOTP()?.toLongOrNull() ?: 0) * 1000,
                    initSuccess = true
                ) to null
            }

            is BaseIBankModalOTPViewEvent.ClearOTP -> {
                previousState.copy(otp = "") to null
            }

            is BaseIBankModalOTPViewEvent.OnDismiss -> {
                previousState.copy(otp = "") to BaseIBankModalOTPViewEffect.OnDismiss
            }

            is BaseIBankModalOTPViewEvent.OnOTPChanged -> {
                when (event.keyInput) {
                    is KeyInput.Delete -> {
                        if (previousState.otp.isNotEmpty()) {
                            previousState.copy(otp = previousState.otp.dropLast(1)) to null
                        } else previousState to null
                    }

                    is KeyInput.Number -> {
                        if (previousState.otp.length < 6) {
                            return previousState.copy(otp = previousState.otp + event.keyInput.value) to null
                        } else previousState to null
                    }

                    else -> {
                        previousState to null
                    }
                }
            }

            is BaseIBankModalOTPViewEvent.OnRetrySendOTP -> {
                previousState to BaseIBankModalOTPViewEffect.RetrySendOTP(
                    previousState.inputData?.getMethod() ?: "",
                    previousState.inputData?.getUserNameInfo() ?: "",
                    previousState.reSendOTPData?.getTransactionID()
                        ?: previousState.inputData?.getTransactionID() ?: ""
                )
            }

            is BaseIBankModalOTPViewEvent.OnRetrySendOTPSuccess -> {
                previousState.copy(
                    retryOTPSuccessNumber = previousState.retryOTPSuccessNumber.plus(1),
                    reSendOTPData = event.data,
                    otp = "",
                    timeoutResendOTP = System.currentTimeMillis() + (event.data?.getRetryTimeSendOTP()?.toLongOrNull() ?: 0) * 1000,
                    timeoutEffectOTP = System.currentTimeMillis() + (event.data?.getActiveTimeOTP()?.toLongOrNull() ?: 0) * 1000
                ) to BaseIBankModalOTPViewEffect.RetryOTPSuccess
            }

            is BaseIBankModalOTPViewEvent.OnVerifyOTPSuccess -> {
                previousState.copy(verifyOTPData = event.data) to BaseIBankModalOTPViewEffect.VerifyOTPSuccess(
                    event.data
                )
            }

            is BaseIBankModalOTPViewEvent.OnVerifyOTPError -> {
                previousState to BaseIBankModalOTPViewEffect.VerifyOTPError(
                    errorCode = event.errorCode,
                    errorMessage = event.errorMessage
                )
            }

            is BaseIBankModalOTPViewEvent.OnVerifyOTP -> {
                previousState to BaseIBankModalOTPViewEffect.VerifyOTP(
                    previousState.inputData?.getMethod() ?: "",
                    previousState.inputData?.getUserNameInfo() ?: "",
                    previousState.reSendOTPData?.getTransactionID()
                        ?: previousState.inputData?.getTransactionID() ?: "",
                    previousState.otp,
                )
            }

            is BaseIBankModalOTPViewEvent.OnFillOTPFromSMS -> {
                previousState.copy(otp = event.otp) to null
            }

            else -> previousState to null
        }
    }
}
