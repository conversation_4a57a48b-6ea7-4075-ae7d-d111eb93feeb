package vn.com.bidv.feature.login.ui.forgotpassword

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.common.extenstion.isNotNull
import vn.com.bidv.feature.login.domain.ForgotPasswordUseCase
import vn.com.bidv.feature.login.ui.forgotpassword.ForgotPasswordReducer.ForgotPasswordViewEffect
import vn.com.bidv.feature.login.ui.forgotpassword.ForgotPasswordReducer.ForgotPasswordViewEvent
import vn.com.bidv.feature.login.ui.forgotpassword.ForgotPasswordReducer.ForgotPasswordViewState
import vn.com.bidv.sdkbase.domain.DomainErrorCode
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class ForgotPasswordViewModel @Inject constructor(
    private val forgotPasswordUseCase: ForgotPasswordUseCase,
) : ViewModelIBankBase<ForgotPasswordViewState, ForgotPasswordViewEvent, ForgotPasswordViewEffect>(
    initialState = ForgotPasswordViewState(),
    reducer = ForgotPasswordReducer()
) {
    override fun handleEffect(
        sideEffect: ForgotPasswordViewEffect,
        onResult: (ForgotPasswordViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is ForgotPasswordViewEffect.ValidPhoneNumberEffect -> {
                callDomain(
                    showLoadingIndicator = false,
                    listErrorCodeListen = listOf(DomainErrorCode.CLIENT_ERROR_CODE),
                    onSuccess = {
                        onResult(ForgotPasswordViewEvent.ValidEmailOrPhoneNumberInputDoneSuccess)
                    },
                    onFail = {
                        onResult(
                            ForgotPasswordViewEvent.ValidOtherInputDoneFail(
                                listClientErrorCode = it?.listClientErrorCode ?: listOf()
                            )
                        )
                    }
                ) {
                    forgotPasswordUseCase.validPhoneNumber(sideEffect.phoneNumber)
                }
            }

            is ForgotPasswordViewEffect.ValidEmailEffect -> {
                callDomain(
                    showLoadingIndicator = false,
                    listErrorCodeListen = listOf(DomainErrorCode.CLIENT_ERROR_CODE),
                    onSuccess = {
                        onResult(ForgotPasswordViewEvent.ValidEmailOrPhoneNumberInputDoneSuccess)
                    },
                    onFail = {
                        if (it.isNotNull()) {
                            onResult(
                                ForgotPasswordViewEvent.ValidOtherInputDoneFail(
                                    listClientErrorCode = it?.listClientErrorCode ?: listOf()
                                )
                            )
                        }
                    }
                ) {
                    forgotPasswordUseCase.validEmail(sideEffect.email)
                }
            }

            is ForgotPasswordViewEffect.ValidateDataEffect -> {
                callDomain(
                    showLoadingIndicator = false,
                    listErrorCodeListen = listOf(DomainErrorCode.CLIENT_ERROR_CODE),
                    onSuccess = {
                        onResult(ForgotPasswordViewEvent.ValidOtherInputDoneSuccess)
                    },
                    onFail = {
                        if (it.isNotNull()) {
                            onResult(
                                ForgotPasswordViewEvent.ValidOtherInputDoneFail(
                                    listClientErrorCode = it?.listClientErrorCode ?: listOf()
                                )
                            )
                        }
                    }
                ) {
                    forgotPasswordUseCase.validateInput(
                        username = sideEffect.username,
                        idNumber = sideEffect.idNumber,
                        expirationDate = sideEffect.expirationDate,
                        methodOtp = sideEffect.methodOtp,
                        contactInfo = sideEffect.contactInfo,
                        registrationNumber = sideEffect.registrationNumber,
                        isIndefinite = sideEffect.isIndefinite
                    )
                }
            }

            is ForgotPasswordViewEffect.SubmitDataEffect -> {
                val mobile =
                    if (sideEffect.methodOtp == MethodOtp.SMS) sideEffect.contactInfo else null
                val otpEmail =
                    if (sideEffect.methodOtp == MethodOtp.EMAIL) sideEffect.contactInfo else null

                callDomain(
                    onSuccess = { result ->
                        onResult(ForgotPasswordViewEvent.SubmitDataSuccessEvent(result.data))
                    },
                ) {
                    forgotPasswordUseCase.forgotPwCreate(
                        username = sideEffect.username,
                        idExpireDate = sideEffect.expirationDate,
                        idNum = sideEffect.idNumber,
                        idReg = sideEffect.registrationNumber,
                        mobile = mobile,
                        otpEmail = otpEmail
                    )
                }
            }

            is ForgotPasswordViewEffect.GoToVerifyQuestionEffect -> {}
            is ForgotPasswordViewEffect.GoToSmsOtpEffect -> {}
        }
    }
}

