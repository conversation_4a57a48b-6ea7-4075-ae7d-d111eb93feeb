package vn.com.bidv.feature.login.domain

import com.google.gson.Gson
import vn.com.bidv.feature.common.domain.verifyFlowUseCase.VerifyByTypeTransactionUseCase
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InitVerifyTransactionResponse
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyCreateTransaction
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyTransaction
import vn.com.bidv.feature.login.data.BiometricRepository
import vn.com.bidv.feature.login.domain.model.BiometricOnRequestDMO
import vn.com.bidv.feature.login.domain.model.CompositeOtpResDMO
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class BiometricVerifyUseCase @Inject constructor(
    private val biometricRepository: BiometricRepository,
) : VerifyByTypeTransactionUseCase {

    override suspend fun initTransaction(input: InputVerifyTransaction): DomainResult<InitVerifyTransactionResponse> {
        return DomainResult.Success(InitVerifyTransactionResponse())
    }

    override suspend fun initCreateTransaction(input: InputVerifyCreateTransaction): DomainResult<InitVerifyTransactionResponse> {
        val biometricOnRequestDMO =
            Gson().fromJson(input.dataString, BiometricOnRequestDMO::class.java)
        val result = biometricRepository.turnOnBiometric(
            biometricOnRequestDMO.credentialId,
            biometricOnRequestDMO.type,
            biometricOnRequestDMO.publicKey
        )
        val domain = result.convert(CompositeOtpResDMO::class.java)
        return if (domain is DomainResult.Success) {
            val transAuthDMO = domain.data?.smartOtp?.copy(
                transKey = domain.data?.transId
            )
            DomainResult.Success(InitVerifyTransactionResponse(transAuth = transAuthDMO))
        } else {
            val domainError = domain as? DomainResult.Error
            DomainResult.Error(domainError?.errorCode ?: "", domainError?.errorMessage)
        }
    }

    override suspend fun verifyTransaction(
        initResponse: InitVerifyTransactionResponse,
        reqValue: String?
    ): DomainResult<String> {
        val result = biometricRepository.verifySmartOtpBiometric(initResponse.transKey ?: initResponse.transAuth?.transKey ?: "", reqValue ?: "")
        val domain = result.convert {
            isSuccess()
        }
        return if (domain is DomainResult.Success) {
            DomainResult.Success(true.toString())
        } else {
            val domainError = domain as? DomainResult.Error
            DomainResult.Error(domainError?.errorCode ?: "", domainError?.errorMessage)

        }
    }
}