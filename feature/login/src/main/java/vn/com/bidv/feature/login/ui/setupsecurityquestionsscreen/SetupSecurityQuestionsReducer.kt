package vn.com.bidv.feature.login.ui.setupsecurityquestionsscreen

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.common.data.utilities.model.SecurityQuestionAnswerDto
import vn.com.bidv.feature.common.domain.data.TransAuthDMO
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.domain.model.CodeValueDMO
import vn.com.bidv.feature.login.domain.model.SecQnGetAllResponseDMO
import vn.com.bidv.feature.login.domain.model.SecurityQuestionAnswerDMO
import vn.com.bidv.feature.login.ui.setupsecurityquestionsscreen.SetupSecurityQuestionsReducer.SetupSecurityQuestionsSideEffect
import vn.com.bidv.feature.login.ui.setupsecurityquestionsscreen.SetupSecurityQuestionsReducer.SetupSecurityQuestionsViewEvent
import vn.com.bidv.feature.login.ui.setupsecurityquestionsscreen.SetupSecurityQuestionsReducer.SetupSecurityQuestionsViewState
import vn.com.bidv.feature.login.ui.setupsecurityquestionsscreen.model.QaModel

class SetupSecurityQuestionsReducer :
    Reducer<SetupSecurityQuestionsViewState, SetupSecurityQuestionsViewEvent, SetupSecurityQuestionsSideEffect> {

    data class SetupSecurityQuestionsViewState(
        val data: List<QaModel> = listOf(
            QaModel(),
            QaModel(),
            QaModel()
        ),
        var dataListQuestion: List<CodeValueDMO> = emptyList(),
        var initData: List<SecurityQuestionAnswerDMO>? = emptyList(),
        val transUpdateResDMO: TransAuthDMO? = null
    ) : ViewState {
        fun dataListQuestionCanSelect(index: Int? = -1): List<CodeValueDMO> {
            return when (index) {
                in data.indices -> {
                    val excludedCodes = data
                        .filterIndexed { i, _ -> i != index }
                        .map { it.question.code }

                    dataListQuestion.filter { item -> item.code !in excludedCodes }
                }

                else -> dataListQuestion
            }
        }

        val isChangeData: Boolean
            get() {
                return data.indices.any { i ->
                    val current = data[i]
                    val initial = initData?.get(i)
                    current.question.code != initial?.questionCode ||
                            current.answerTextFieldValue != initial?.answer
                }
            }

    }

    @Immutable
    sealed class SetupSecurityQuestionsViewEvent : ViewEvent {

        data class InitEvent(val initData: List<SecurityQuestionAnswerDMO> = emptyList()) :
            SetupSecurityQuestionsViewEvent()

        data class GetQuestionSuccessEvent(val initData: SecQnGetAllResponseDMO?) :
            SetupSecurityQuestionsViewEvent()

        data class QuestionChangeEvent(val index: Int? = -1, val question: CodeValueDMO) :
            SetupSecurityQuestionsViewEvent()

        data class AnswerChangeEvent(val index: Int? = -1, val answer: String) :
            SetupSecurityQuestionsViewEvent()

        data object SubmitDataEvent : SetupSecurityQuestionsViewEvent()

        data object SubmitDataWithAuthenticationEvent : SetupSecurityQuestionsViewEvent()

        data object SubmitDataSuccessEvent :
            SetupSecurityQuestionsViewEvent()

        data class SubmitDataWithAuthenticationSuccessEvent(val listSubmit: List<SecurityQuestionAnswerDto>) :
            SetupSecurityQuestionsViewEvent()

        data class SubmitDataErrorEvent(val listClientErrorCode: List<Pair<String, String>>) :
            SetupSecurityQuestionsViewEvent()

        data object BackToOnlyViewEvent : SetupSecurityQuestionsViewEvent()

    }

    @Immutable
    sealed class SetupSecurityQuestionsSideEffect : SideEffect {

        data object GetAllQuestionEffect : SetupSecurityQuestionsSideEffect()

        data object GetAllQuestionSuccessEffect : SetupSecurityQuestionsSideEffect(), UIEffect

        data class SubmitDataEffect(
            val dataSubmit: List<SecurityQuestionAnswerDMO>,
        ) : SetupSecurityQuestionsSideEffect()

        data class SubmitDataWithAuthenticationEffect(
            val dataSubmit: List<SecurityQuestionAnswerDMO>,
        ) : SetupSecurityQuestionsSideEffect()

        data object SubmitDataSuccessEffect :
            SetupSecurityQuestionsSideEffect(), UIEffect

        data class SubmitDataWithAuthenticationSuccessEffect(val listSubmit: List<SecurityQuestionAnswerDto>) :
            SetupSecurityQuestionsSideEffect(), UIEffect

        data object ApproveSaveDataWithAuthenticationSuccessEffect :
            SetupSecurityQuestionsSideEffect(), UIEffect

    }

    override fun reduce(
        previousState: SetupSecurityQuestionsViewState,
        event: SetupSecurityQuestionsViewEvent,
    ): Pair<SetupSecurityQuestionsViewState, SetupSecurityQuestionsSideEffect?> {

        return when (event) {
            is SetupSecurityQuestionsViewEvent.InitEvent -> {
                val newState = if (event.initData.isEmpty()) {
                    previousState
                } else previousState.copy(
                    initData = event.initData
                )
                return newState to SetupSecurityQuestionsSideEffect.GetAllQuestionEffect
            }

            is SetupSecurityQuestionsViewEvent.QuestionChangeEvent -> {
                val updatedData = previousState.data.toMutableList()
                event.index?.let { index ->
                    if (index in updatedData.indices && event.question.code != updatedData[index].question.code) {
                        updatedData[index] = updatedData[index].copy(
                            question = event.question,
                            answerTextFieldValue = "",
                            listErrorCode = updatedData[index].listErrorCode.orEmpty()
                                .filter { it.first != Constants.ViewID.QUESTION_VIEW_ID + index },
                        )
                    }
                }
                val newState = previousState.copy(data = updatedData)
                return newState to null
            }

            is SetupSecurityQuestionsViewEvent.AnswerChangeEvent -> {
                val updatedData = previousState.data.toMutableList()
                event.index?.let { index ->
                    if (index in updatedData.indices) {
                        updatedData[index] =
                            updatedData[index].copy(
                                answerTextFieldValue = event.answer,
                                listErrorCode = updatedData[index].listErrorCode.orEmpty()
                                    .filter { it.first != Constants.ViewID.ANSWER_VIEW_ID + index }
                            )
                    }
                }
                val newState = previousState.copy(data = updatedData)
                return newState to null
            }

            is SetupSecurityQuestionsViewEvent.SubmitDataEvent -> {
                val newEffect =
                    if (previousState.data.isEmpty()) null else SetupSecurityQuestionsSideEffect.SubmitDataEffect(
                        dataSubmit = previousState.data.map { qaModel ->
                            SecurityQuestionAnswerDMO(
                                questionCode = qaModel.question.code,
                                answer = qaModel.answerTextFieldValue?.trim() ?: ""
                            )
                        }
                    )
                return previousState to newEffect
            }

            is SetupSecurityQuestionsViewEvent.SubmitDataWithAuthenticationEvent -> {
                val newEffect =
                    if (!previousState.isChangeData) SetupSecurityQuestionsSideEffect.ApproveSaveDataWithAuthenticationSuccessEffect
                    else SetupSecurityQuestionsSideEffect.SubmitDataWithAuthenticationEffect(
                        dataSubmit = previousState.data.map { qaModel ->
                            SecurityQuestionAnswerDMO(
                                questionCode = qaModel.question.code,
                                answer = qaModel.answerTextFieldValue?.trim() ?: ""
                            )
                        }
                    )
                return previousState to newEffect
            }

            is SetupSecurityQuestionsViewEvent.SubmitDataSuccessEvent -> {
                previousState to SetupSecurityQuestionsSideEffect.SubmitDataSuccessEffect
            }

            is SetupSecurityQuestionsViewEvent.GetQuestionSuccessEvent -> {
                val newState =
                    previousState.copy(
                        dataListQuestion = event.initData?.items ?: emptyList(),
                        data = if (!previousState.initData.isNullOrEmpty()) {
                            previousState.initData?.map { item ->
                                QaModel(
                                    question = CodeValueDMO(
                                        code = item.questionCode,
                                        value = event.initData?.items?.find { it.code == item.questionCode }?.value
                                            ?: "",
                                    ),
                                    answerTextFieldValue = item.answer
                                )
                            } ?: previousState.data
                        } else previousState.data
                    )

                return newState to SetupSecurityQuestionsSideEffect.GetAllQuestionSuccessEffect
            }

            is SetupSecurityQuestionsViewEvent.SubmitDataWithAuthenticationSuccessEvent -> {
                previousState to SetupSecurityQuestionsSideEffect.SubmitDataWithAuthenticationSuccessEffect(
                    listSubmit = event.listSubmit
                )
            }

            is SetupSecurityQuestionsViewEvent.SubmitDataErrorEvent -> {
                previousState.copy(
                    data = previousState.data.mapIndexed { index, qaModel ->
                        qaModel.copy(
                            listErrorCode = event.listClientErrorCode
                                .filter { it.first == Constants.ViewID.QUESTION_VIEW_ID + index || it.first == Constants.ViewID.ANSWER_VIEW_ID + index },
                        )
                    }
                ) to null
            }

            SetupSecurityQuestionsViewEvent.BackToOnlyViewEvent -> {
                val dataListQuestion = previousState.dataListQuestion
                previousState.copy(
                    data = previousState.initData?.map { item ->
                    QaModel(
                        question = CodeValueDMO(
                            code = item.questionCode,
                            value = dataListQuestion.find { it.code == item.questionCode }?.value
                                ?: "",
                        ),
                        answerTextFieldValue = item.answer
                    )
                } ?: previousState.data) to null
            }
        }

    }

}
