package vn.com.bidv.feature.login.ui.approvalrequestsresult

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.google.gson.Gson
import vn.com.bidv.designsystem.component.dataentry.IBankInformation
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankActionBar
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.feature.login.ui.approvalrequestsresult.ApprovalRequestsResultReducer.ApprovalRequestsResultViewEvent
import vn.com.bidv.feature.login.ui.approvalrequestsresult.ApprovalRequestsResultReducer.ApprovalRequestsResultViewState
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.login.domain.model.AdminTransVerifyDMO
import vn.com.bidv.feature.login.ui.manageapprovalrequest.model.Action
import vn.com.bidv.sdkbase.navigation.IBankMainRouting
import vn.com.bidv.sdkbase.navigation.NavigationHelper as SdkBaseNavigationHelper
import vn.com.bidv.designsystem.R as RDesignSystem
import vn.com.bidv.localization.R as RLocalization

@Composable
fun ApprovalRequestsResultScreen(
    navController: NavHostController,
    jsonData: String?
) {
    val vm: ApprovalRequestsResultViewModel = hiltViewModel()
    BaseScreen(
        navController = navController,
        viewModel = vm,
        renderContent = { uiState, onEvent ->
            if (!uiState.initSuccess) {
                val model = try {
                    Gson().fromJson(
                        jsonData,
                        AdminTransVerifyDMO::class.java
                    )
                } catch (ex: Exception) {
                    AdminTransVerifyDMO()
                }
                onEvent(ApprovalRequestsResultViewEvent.OnInitScreen(model))
            }
            ApprovalRequestsResultContent(uiState, onEvent, navController)
        },
        handleSideEffect = { sideEffect ->

        },
        topAppBarType = TopAppBarType.Result,
        topAppBarConfig = TopAppBarConfig(
            iconLeading = RDesignSystem.drawable.bidv_direct_logo
        )
    )
}

@Composable
private fun ApprovalRequestsResultContent(
    uiState: ApprovalRequestsResultViewState,
    onEvent: (ApprovalRequestsResultViewEvent) -> Unit,
    navController: NavHostController,
) {
    Box(modifier = Modifier.fillMaxSize()) {
        if (uiState.dataVerify.total == 1) {
            SingleApprovalRequestsResultContent(
                dataVerify = uiState.dataVerify,
            )
        } else {
            MultiApprovalRequestsResultContent(
                dataVerify = uiState.dataVerify,
            )
        }

        IBankActionBar(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.BottomCenter)
                .background(Color.White),
            isVertical = false,
            buttonPositive = DialogButtonInfo(
                label = stringResource(RLocalization.string.phe_duyet_yeu_cau_khac)
            ) {
                SdkBaseNavigationHelper.navigateWithPopUpTo(
                    navController = navController,
                    targetRoute = IBankMainRouting.AuthRoutes.ManageApprovalRequestsRoute.route,
                    popUpToRoute = IBankMainRouting.AuthRoutes.ManageApprovalRequestsRoute.route
                )
            },
        )
    }
}

@Composable
fun MultiApprovalRequestsResultContent(
    dataVerify: AdminTransVerifyDMO,
) {
    Column(modifier = Modifier.fillMaxWidth()) {
        ApprovalRequestsStatus(data = dataVerify)
        Spacer(modifier = Modifier.size(IBSpacing.spacingM))
        MultiRequestsContent(dataVerify)
    }
}

@Composable
fun MultiRequestsContent(dataVerify: AdminTransVerifyDMO) {
    val itemCount = if ((dataVerify.totalSuccess ?: 0) > 0) {
        dataVerify.totalSuccess
    } else dataVerify.totalError
    Column(modifier = Modifier.fillMaxWidth()) {
        Text(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(
                RLocalization.string.s_yeu_cau,
                "$itemCount/${dataVerify.total}"
            ),
            style = LocalTypography.current.headlineHeadline_s,
            color = LocalColorScheme.current.contentMainPrimary,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
fun SingleApprovalRequestsResultContent(
    dataVerify: AdminTransVerifyDMO,
) {
    Column(modifier = Modifier.fillMaxWidth()) {
        ApprovalRequestsStatus(data = dataVerify)
        Spacer(modifier = Modifier.size(IBSpacing.spacingM))
        SingleRequestContent(dataVerify = dataVerify)
    }
}

@Composable
fun SingleRequestContent(
    dataVerify: AdminTransVerifyDMO,
) {

    val transInfo = dataVerify.transInfo
    val error = dataVerify.errors

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = IBSpacing.spacingM)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(IBSpacing.spacingXs))
                .background(LocalColorScheme.current.bgMainTertiary)
                .padding(horizontal = IBSpacing.spacingM, vertical = IBSpacing.spacingS),
        ) {
            transInfo?.fullName?.let {
                IBankInformation(
                    modifier = Modifier.fillMaxWidth(),
                    label = stringResource(RLocalization.string.ten_nguoi_dung),
                    dataValue = it,
                )
            }
            transInfo?.userName?.let {
                IBankInformation(
                    modifier = Modifier.fillMaxWidth(),
                    label = stringResource(RLocalization.string.ten_dang_nhap),
                    dataValue = it,
                )
            }
            transInfo?.requestType?.let {
                IBankInformation(
                    modifier = Modifier.fillMaxWidth(),
                    label = stringResource(RLocalization.string.loai_yeu_cau),
                    dataValue = it,
                )
            }

            val firstValidError = error?.firstOrNull()
            firstValidError?.errorDesc?.let {
                IBankInformation(
                    modifier = Modifier.fillMaxWidth(),
                    label = stringResource(RLocalization.string.mo_ta_loi),
                    dataValue = it
                )
            }
        }
    }
}

@Composable
fun ApprovalRequestsStatus(
    data: AdminTransVerifyDMO,
) {
    val (icon, message) = if ((data.totalSuccess ?: 0) > 0) {
        Pair(
            painterResource(id = RDesignSystem.drawable.icon_popup_success_64),
            stringResource(
                if (data.actionType == Action.APPROVE.name) {
                    RLocalization.string.phe_duyet_thanh_cong
                } else RLocalization.string.tu_choi_thanh_cong
            )
        )
    } else {
        Pair(
            painterResource(id = RDesignSystem.drawable.icon_popup_error_64),
            stringResource(
                if (data.actionType == Action.APPROVE.name) {
                    RLocalization.string.phe_duyet_khong_thanh_cong
                } else RLocalization.string.tu_choi_khong_thanh_cong
            )
        )
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = IBSpacing.spacingM),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        Icon(
            painter = icon,
            contentDescription = null,
            tint = Color.Unspecified,
            modifier = Modifier.size(64.dp)
        )
        Spacer(modifier = Modifier.size(IBSpacing.spacingXs))
        Text(
            modifier = Modifier.fillMaxWidth(),
            text = message,
            style = LocalTypography.current.bodyBody_l,
            color = LocalColorScheme.current.contentMainPrimary,
            textAlign = TextAlign.Center
        )
    }
}
