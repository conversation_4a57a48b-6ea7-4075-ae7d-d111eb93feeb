package vn.com.bidv.feature.login.ui.smsOTP.changepin

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import vn.com.bidv.common.di.IoDispatcher
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.domain.ActiveSmartOTPUseCase
import vn.com.bidv.feature.login.domain.model.TransUpdateBasicOtpDMO
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPReducer
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPViewModel
import javax.inject.Inject

@HiltViewModel
class ChangePinModalOTPViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val activeSmartOTPUseCase: ActiveSmartOTPUseCase,
) : BaseIBankModalOTPViewModel<TransUpdateBasicOtpDMO, TransUpdateBasicOtpDMO, Boolean>(
    initialState = BaseIBankModalOTPReducer.BaseIBankModalOTPViewState(),
    reducer = BaseIBankModalOTPReducer()
) {

    override fun handleEffect(
        sideEffect: BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect<TransUpdateBasicOtpDMO, TransUpdateBasicOtpDMO, Boolean>,
        onResult: (BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent<TransUpdateBasicOtpDMO, TransUpdateBasicOtpDMO, Boolean>) -> Unit
    ) {
        when (sideEffect) {
            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.RetrySendOTP -> {
                callDomain(
                    onSuccess = { result ->
                        onResult(
                            BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnRetrySendOTPSuccess(
                                result.data
                            )
                        )
                    },

                    ) {
                    activeSmartOTPUseCase.changePin()
                }
            }

            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTP -> {
                callDomain(
                    isListenAllError = true,
                    onSuccess = { result ->
                        shareChangePinSuccessAction {
                            activeSmartOTPUseCase.shareChangePinSuccessAction(Constants.CHANGE_PIN_SUCCESS)
                        }
                        onResult(
                            BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnVerifyOTPSuccess(
                                result.data
                            )
                        )
                    },
                    onFail = {
                        onResult(
                            BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnVerifyOTPError(
                                it?.errorCode ?: "",
                                it?.errorMessage
                            )
                        )
                    }

                ) {
                    activeSmartOTPUseCase.verifyBasicOtp(
                        transKey = sideEffect.txnId,
                        authValue = sideEffect.otpNum
                    )
                }
            }

            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTPError,
            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.RetryOTPSuccess,
            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTPSuccess -> TODO()
        }
    }

    private fun shareChangePinSuccessAction(onShare: suspend () -> Unit) {
        viewModelScope.launch(Dispatchers.Default) {
            onShare()
        }
    }

    fun clearDataInputPin() {
        viewModelScope.launch(Dispatchers.Default) {
            activeSmartOTPUseCase.clearDataInputPin()
        }
    }
}
