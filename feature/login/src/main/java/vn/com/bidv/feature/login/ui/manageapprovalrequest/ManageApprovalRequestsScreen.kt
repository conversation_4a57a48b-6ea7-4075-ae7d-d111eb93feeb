@file:OptIn(ExperimentalMaterial3Api::class)

package vn.com.bidv.feature.login.ui.manageapprovalrequest

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.EmptyStateType
import vn.com.bidv.designsystem.component.IBankEmptyState
import vn.com.bidv.designsystem.component.dataentry.IBFrameState
import vn.com.bidv.feature.login.ui.manageapprovalrequest.ManageApprovalRequestsReducer.ManageApprovalRequestsViewEvent
import vn.com.bidv.feature.login.ui.manageapprovalrequest.ManageApprovalRequestsReducer.ManageApprovalRequestsViewState
import vn.com.bidv.designsystem.component.dataentry.IBankCheckBox
import vn.com.bidv.designsystem.component.dataentry.IBankInformation
import vn.com.bidv.designsystem.component.dataentry.IBankInputSearchBase
import vn.com.bidv.designsystem.component.dataentry.RemoveSpecialCharacterFilterCNR
import vn.com.bidv.designsystem.component.dataentry.datacard.IBankDataCard
import vn.com.bidv.designsystem.component.feedback.bottomsheet.IBankBottomSheet
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.component.feedback.modelreason.IBankModalReason
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonType
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankActionBar
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.designsystem.ui.listwithloadmorev2.ListAutoLoadMore
import vn.com.bidv.designsystem.ui.listwithloadmorev2.ListAutoLoadMoreReducer
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.ModelCheckAble
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.SimpleSearchRuleFilters
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionTypeConstant
import vn.com.bidv.feature.login.domain.model.AdminTransVerifyDMO
import vn.com.bidv.feature.login.domain.model.TransRequestApprovalDMO
import vn.com.bidv.feature.login.navigation.NavigationHelper
import vn.com.bidv.feature.login.ui.manageapprovalrequest.model.Action
import vn.com.bidv.feature.login.ui.manageapprovalrequest.model.ItemMenuDropDown
import vn.com.bidv.feature.login.ui.manageapprovalrequest.model.ManageApprovalPopupState
import vn.com.bidv.sdkbase.utils.VietnameseAccentRemoverFilter
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants.DateTimeConstants.FORMAT_DD_MM_YYYY_HH_MM_SS
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants.DateTimeConstants.INPUT_FORMAT_DD_MM_YYYY_HH_MM_SS_SSSSSS
import vn.com.bidv.sdkbase.utils.exts.toFormattedDate
import vn.com.bidv.localization.R as RLocalization
import vn.com.bidv.designsystem.R as RDesignSystem
import vn.com.bidv.feature.common.navigation.NavigationHelper as CommonNavigationHelper

@Composable
fun ManageApprovalRequestsScreen(navController: NavHostController) {
    val vm: ManageApprovalRequestsViewModel = hiltViewModel()
    val manageListTxnPendingViewModel: ManageListTxnPendingViewModel = hiltViewModel()

    var verifyOtpMessageError by remember { mutableStateOf("") }
    var detailItem by remember { mutableStateOf<TransRequestApprovalDMO?>(null) }
    var isShowDetailBottomSheet by remember { mutableStateOf(false) }
    var approvalPopupState by remember { mutableStateOf(ManageApprovalPopupState()) }
    var rejectPopupState by remember { mutableStateOf(ManageApprovalPopupState()) }

    BaseScreen(
        navController = navController,
        viewModel = vm,
        renderContent = { uiState, onEvent ->

            if (approvalPopupState.isVisible) {
                ShowConfirmModal(
                    title = approvalPopupState.title,
                    supportingText = approvalPopupState.supportText,
                    onEvent = {
                        navigateToVerifyTransaction(navController, approvalPopupState.selectedTransactions.map { it.id ?: "" })
                    },
                    onDismiss = {
                        approvalPopupState = ManageApprovalPopupState()
                    }
                )
            }

            if (rejectPopupState.isVisible) {
                ShowRejectModal(
                    title = rejectPopupState.title,
                    onSubmit = { reason ->
                        onEvent(
                            ManageApprovalRequestsViewEvent.OnRejectTransaction(
                                rejectPopupState.selectedTransactions.map { it.id ?: "" },
                                reason,
                                Action.REJECT.name
                            ))
                    },
                    onDismiss = {
                        rejectPopupState = ManageApprovalPopupState()
                    }
                )
            }

            if (isShowDetailBottomSheet) {
                detailItem?.let {
                    ShowDetailBottomSheet(
                        transRequestApprovalDMO = it,
                        onEvent = onEvent,
                        onDismiss = {
                            isShowDetailBottomSheet = false
                            detailItem = null
                        }
                    )
                }
            }

            ManageApprovalRequestsContent(
                uiState,
                onEvent,
                manageListTxnPendingViewModel,
                navController
            )

            if (verifyOtpMessageError.isNotEmpty()) {
                IBankModalConfirm(
                    title = stringResource(RLocalization.string.loi),
                    modalConfirmType = ModalConfirmType.Error,
                    supportingText = verifyOtpMessageError,
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(RLocalization.string.thu_lai),
                            onClick = {
                                navigateToVerifyTransaction(navController, uiState.listTranRequestApprovalProcessing.map { it.id ?: "" })
                            }
                        )
                    ),
                    onDismissRequest = {
                        verifyOtpMessageError = ""
                    }
                )
            }
        },
        handleSideEffect = { sideEffect ->
            when (sideEffect) {
                is ManageApprovalRequestsReducer.ManageApprovalRequestsViewEffect.RejectTransactionSuccess -> {
                    isShowDetailBottomSheet = false
                    navigateToApprovalRequestResult(navController, sideEffect.data)
                }

                is ManageApprovalRequestsReducer.ManageApprovalRequestsViewEffect.ShowConfirmPopup -> {
                    isShowDetailBottomSheet = false
                    when (sideEffect.action) {
                        Action.APPROVE -> {
                            approvalPopupState = ManageApprovalPopupState(
                                isVisible = true,
                                title = navController.context.getString(RLocalization.string.xac_nhan_phe_duyet),
                                supportText = navController.context.getString(
                                    if (sideEffect.selectedTransactions.size == 1)
                                        RLocalization.string.quy_khach_chac_chan_phe_duyet_yeu_cau_nay
                                    else
                                        RLocalization.string.quy_khach_chac_chan_phe_duyet_s_yeu_cau_nay,
                                    sideEffect.selectedTransactions.size.toString()
                                ),
                                selectedTransactions = sideEffect.selectedTransactions
                            )
                        }

                        Action.REJECT -> {
                            rejectPopupState = ManageApprovalPopupState(
                                isVisible = true,
                                title = navController.context.getString(
                                    if (sideEffect.selectedTransactions.size == 1)
                                        RLocalization.string.tu_choi_yeu_cau
                                    else
                                        RLocalization.string.tu_choi_s_yeu_cau,
                                    sideEffect.selectedTransactions.size.toString()
                                ),
                                selectedTransactions = sideEffect.selectedTransactions
                            )
                        }
                    }
                }

                is ManageApprovalRequestsReducer.ManageApprovalRequestsViewEffect.ShowDetailBottomSheet -> {
                    isShowDetailBottomSheet = true
                    detailItem = sideEffect.transRequestApprovalDMO
                }

                else -> {
                    //nothing
                }
            }
        },
        topAppBarType = TopAppBarType.Title,
        topAppBarConfig = TopAppBarConfig(
            titleTopAppBar = stringResource(RLocalization.string.quan_ly_yeu_cau_cho_duyet)
        )
    )
}

fun navigateToApprovalRequestResult(
    navController: NavHostController,
    data: AdminTransVerifyDMO,
) {
    NavigationHelper.navigateToApprovalRequestsResultScreen(navController, data)
}

fun navigateToVerifyTransaction(navController: NavHostController, ids: List<String>) {
    CommonNavigationHelper.navigateToVerifyByTypeTransaction(
        navController = navController,
        txnIds = ids,
        type = VerifyTransactionTypeConstant.ManageApprovalRequests
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ShowDetailBottomSheet(
    transRequestApprovalDMO: TransRequestApprovalDMO,
    onEvent: (ManageApprovalRequestsViewEvent) -> Unit,
    onDismiss: () -> Unit
) {
    IBankBottomSheet(
        title = stringResource(RLocalization.string.chi_tiet_yeu_cau),
        onDismiss = onDismiss,
    ) {
        Column(modifier = Modifier.fillMaxWidth()) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = IBSpacing.spacingM)
            ) {
                transRequestApprovalDMO.fullName?.let {
                    IBankInformation(
                        label = stringResource(RLocalization.string.ten_nguoi_dung),
                        dataValue = it,
                    )
                }
                transRequestApprovalDMO.createdBy?.let {
                    IBankInformation(
                        label = stringResource(RLocalization.string.ten_dang_nhap),
                        dataValue = it,
                    )
                }
                transRequestApprovalDMO.roleGroup?.let {
                    IBankInformation(
                        label = stringResource(RLocalization.string.nhom_quyen),
                        dataValue = it,
                    )
                }
                transRequestApprovalDMO.phoneNumber?.let {
                    IBankInformation(
                        label = stringResource(RLocalization.string.so_dien_thoai),
                        dataValue = it,
                    )
                }
                transRequestApprovalDMO.otpEmail?.let {
                    IBankInformation(
                        label = stringResource(RLocalization.string.email_nhan_otp),
                        dataValue = it,
                    )
                }
                transRequestApprovalDMO.requestType?.let {
                    IBankInformation(
                        label = stringResource(RLocalization.string.loai_yeu_cau),
                        dataValue = it,
                    )
                }
                transRequestApprovalDMO.createdDate?.let {
                    IBankInformation(
                        label = stringResource(RLocalization.string.thoi_gian_yeu_cau),
                        dataValue = it.toFormattedDate(
                            inputPattern = INPUT_FORMAT_DD_MM_YYYY_HH_MM_SS_SSSSSS,
                            outputPattern = FORMAT_DD_MM_YYYY_HH_MM_SS
                        ),
                    )
                }
            }

            BottomActionBar(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White),
                onEvent = onEvent,
                listSelected = listOf(transRequestApprovalDMO)
            )
        }
    }
}

@Composable
private fun ManageApprovalRequestsContent(
    uiState: ManageApprovalRequestsViewState,
    onEvent: (ManageApprovalRequestsViewEvent) -> Unit,
    manageListTxnPendingViewModel: ManageListTxnPendingViewModel,
    navController: NavHostController,
) {
    val listTxnPendingState = rememberLazyListState()
    var onEventListView by remember {
        mutableStateOf<((ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent<TransRequestApprovalDMO, SimpleSearchRuleFilters>) -> Unit)?>(
            null
        )
    }
    var keywordSearch by remember { mutableStateOf("") }

    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current

    Box(modifier = Modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .fillMaxSize()
        ) {
            SearchBox(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = IBSpacing.spacingM),
                onSearchValueChange = { searchText ->
                    keywordSearch = searchText

                    onEventListView?.invoke(
                        ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent.UpdateRuleFilters(
                            SimpleSearchRuleFilters(searchText)
                        )
                    )
                }
            )

            Spacer(modifier = Modifier
                .size(IBSpacing.spacingS)
                .padding(horizontal = IBSpacing.spacingM))

            if (uiState.listTranRequestApproval.isNotEmpty()) {
                Row(modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = IBSpacing.spacingM)) {
                    IBankCheckBox(
                        indeterminate = getItemsCheckedCount(uiState.listTranRequestApproval) != uiState.listTranRequestApproval.size,
                        checked = getItemsCheckedCount(uiState.listTranRequestApproval) > 0,
                        onCheckedChange = { data ->
                            onEventListView?.invoke(
                                ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent.SelectAll(
                                    data.checked
                                )
                            )
                        })
                    Text(
                        text = if (getItemsCheckedCount(uiState.listTranRequestApproval) > 0) stringResource(RLocalization.string.bo_chon)
                        else stringResource(RLocalization.string.chon_tat_ca),
                        style = typography.titleTitle_s,
                        color = colorScheme.contentMainSecondary,
                        modifier = Modifier.padding(start = IBSpacing.spacingXs)
                    )
                    Text(
                        text = if (getItemsCheckedCount(uiState.listTranRequestApproval) > 0) {
                            stringResource(
                                RLocalization.string.da_chon_d,
                                getItemsCheckedCount(uiState.listTranRequestApproval)
                            ).plus("/").plus(uiState.listTranRequestApproval.size)
                        } else {
                            stringResource(
                                RLocalization.string.s_yeu_cau,
                                uiState.listTranRequestApproval.size
                            )
                        },
                        style = typography.bodyBody_m,
                        color = colorScheme.contentMainPrimary,
                        modifier = Modifier.weight(1f),
                        textAlign = TextAlign.End
                    )
                }
            }

            Box(modifier = Modifier
                .weight(1f)
                .padding(horizontal = IBSpacing.spacingM)) {
                ListAutoLoadMore(
                    listState = listTxnPendingState,
                    viewModel = manageListTxnPendingViewModel,
                    registerOnEvent = {
                        onEventListView = it
                    },
                    onStateChange = { currentState ->
                        onEvent(ManageApprovalRequestsViewEvent.OnGetListTxnDataSuccess(currentState.listItems))
                    },
                    emptyView = {
                        if (keywordSearch.isNotEmpty()) {
                            IBankEmptyState(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(top = IBSpacing.spacing3xl),
                                emptyStateType = EmptyStateType.SearchNoResult,
                                backgroundColor = colorScheme.bgMainSecondary,
                                verticalArrangement = Arrangement.Top,
                                supportingText = stringResource(RLocalization.string.khong_ton_tai_yeu_cau_theo_dieu_kien_tim_kiem),
                            )
                        } else NoDataLayout()
                    }
                ) { item ->
                    if (uiState.listTranRequestApproval.isNotEmpty() && item.data.id == uiState.listTranRequestApproval.firstOrNull()?.data?.id) {
                        Spacer(Modifier.height(IBSpacing.spacingS))
                    }
                    TxnPendingApprovalItem(
                        modifier = Modifier.fillMaxWidth(),
                        txnItem = item,
                        onEventListView = onEventListView,
                        onEvent = onEvent,
                        onViewDetailItem = {
                            onEvent(ManageApprovalRequestsViewEvent.OnShowBottomSheet(it))
                        },
                    )
                    Spacer(Modifier.height(IBSpacing.spacingL))
                }
            }

            if (getItemsCheckedCount(uiState.listTranRequestApproval) > 0) {
                val listSelected = uiState.listTranRequestApproval.filter { it.isChecked }.map { it.data }
                BottomActionBar(
                    Modifier
                        .fillMaxWidth()
                        .background(Color.White),
                    onEvent = onEvent,
                    listSelected = listSelected
                )
            }
        }
    }

}

@Composable
private fun BottomActionBar(
    modifier: Modifier,
    onEvent: (ManageApprovalRequestsViewEvent) -> Unit,
    listSelected: List<TransRequestApprovalDMO>,
) {

    IBankActionBar(
        modifier = modifier,
        isVertical = false,
        typeNegativeButton = NormalButtonType.DESSECONDARYCOLOR(LocalColorScheme.current),
        buttonNegative = DialogButtonInfo(
            label = stringResource(RLocalization.string.tu_choi),
            onClick = {
                onEvent(ManageApprovalRequestsViewEvent.OnShowConfirmPopup(Action.REJECT, listSelected))
            }
        ),
        buttonPositive = DialogButtonInfo(
            label = stringResource(RLocalization.string.duyet),
            onClick = {
                onEvent(ManageApprovalRequestsViewEvent.OnShowConfirmPopup(Action.APPROVE, listSelected))
            }
        )
    )
}

@Composable
fun TxnPendingApprovalItemHeader(
    txnItem: ModelCheckAble<TransRequestApprovalDMO>,
    onEvent: (ManageApprovalRequestsViewEvent) -> Unit,
    onEventListView: ((ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent<TransRequestApprovalDMO, SimpleSearchRuleFilters>) -> Unit)?
) {

    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current

    var isShowDropDown by remember { mutableStateOf(false) }

    val listMenu = listOf(
        ItemMenuDropDown(
            RDesignSystem.drawable.success_outline,
            stringResource(RLocalization.string.duyet),
            Action.APPROVE
        ),
        ItemMenuDropDown(
            vn.com.bidv.designsystem.R.drawable.close_outline,
            stringResource(RLocalization.string.tu_choi),
            Action.REJECT
        ),
    )

    Column {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .padding(
                    start = IBSpacing.spacingM,
                    top = IBSpacing.spacingS,
                    end = IBSpacing.spacingM
                )
                .fillMaxWidth()
        ) {
            IBankCheckBox(
                indeterminate = false,
                checked = txnItem.isChecked,
                onCheckedChange = {
                    onEventListView?.invoke(
                        ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent.SelectItem(
                            txnItem
                        )
                    )
                }
            )

            Row(modifier = Modifier.weight(1f)) {
                Text(
                    modifier = Modifier
                        .padding(start = IBSpacing.spacingXs)
                        .fillMaxSize(0.6f),
                    text = txnItem.data.requestType ?: "",
                    style = typography.titleTitle_s,
                    color = colorScheme.contentMainPrimary,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }

            Box {
                Image(
                    painterResource(id = RDesignSystem.drawable.more_vertical_outline),
                    contentDescription = "",
                    Modifier
                        .size(20.dp)
                        .align(Alignment.BottomEnd)
                        .clickable {
                            isShowDropDown = true
                        }
                )
            }
        }

        Spacer(modifier = Modifier.height(IBSpacing.spacingXs))

        Box(Modifier.align(Alignment.End)) {
            DropdownMenu(
                modifier = Modifier.background(colorScheme.bgMainTertiary),
                expanded = isShowDropDown,
                onDismissRequest = {
                    isShowDropDown = false
                }) {
                listMenu.forEachIndexed { _, item ->

                    DropdownMenuItem(
                        text = {
                            Row(
                                modifier = Modifier
                                    .wrapContentSize()
                                    .padding(IBSpacing.spacingXs),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Image(
                                    painter = painterResource(
                                        id = item.idDrawable
                                    ),
                                    contentDescription = "",
                                    modifier = Modifier
                                        .size(IBSpacing.spacingL)
                                )
                                Text(
                                    modifier = Modifier.padding(start = IBSpacing.spacingXs),
                                    text = item.content,
                                    style = typography.bodyBody_m,
                                    color = colorScheme.contentMainSecondary,
                                    textAlign = TextAlign.Center
                                )
                            }
                        },
                        onClick = {
                            isShowDropDown = false
                            onEvent(
                                ManageApprovalRequestsViewEvent.OnShowConfirmPopup(
                                    item.action,
                                    listOf(txnItem.data)
                                )
                            )
                        })
                }
            }
        }

        HorizontalDivider(
            color = colorScheme.borderMainSecondary, thickness = IBBorderDivider.borderDividerS
        )
    }
}

@Composable
private fun TxnPendingApprovalItemBody(
    txnItem: ModelCheckAble<TransRequestApprovalDMO>,
    onViewDetailItem: (TransRequestApprovalDMO) -> Unit = {}
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clickable {
                onViewDetailItem(txnItem.data)
            }
            .padding(
                vertical = IBSpacing.spacingXs,
                horizontal = IBSpacing.spacingM,
            )
    ) {
        IBankInformation(
            label = stringResource(RLocalization.string.ten_nguoi_dung),
            dataValue = txnItem.data.fullName ?: "",
        )

        Spacer(modifier = Modifier.height(IBSpacing.spacingS))
        IBankInformation(
            label = stringResource(RLocalization.string.ten_dang_nhap),
            dataValue = txnItem.data.createdBy ?: "",
        )

        Spacer(modifier = Modifier.height(IBSpacing.spacingS))

        IBankInformation(
            label = stringResource(RLocalization.string.thoi_gian_yeu_cau),
            dataValue = txnItem.data.createdDate?.toFormattedDate(
                inputPattern = INPUT_FORMAT_DD_MM_YYYY_HH_MM_SS_SSSSSS,
                outputPattern = FORMAT_DD_MM_YYYY_HH_MM_SS
            ) ?: "",
        )
    }
}

@Composable
private fun ShowConfirmModal(
    title: String,
    supportingText: String,
    onEvent: () -> Unit,
    onDismiss: () -> Unit
) {
    IBankModalConfirm(
        modalConfirmType = ModalConfirmType.Question,
        title = title,
        supportingText = supportingText,
        listDialogButtonInfo = listOf(
            DialogButtonInfo(
                label = stringResource(RLocalization.string.xac_nhan),
                onClick = {
                    onEvent()
                    onDismiss()
                },
                isDismissRequest = false
            ),
            DialogButtonInfo(
                label = stringResource(RLocalization.string.huy),
            ),
        ),
        onDismissRequest = onDismiss
    )
}

@Composable
private fun ShowRejectModal(
    title: String,
    onSubmit: (reason: String) -> Unit,
    onDismiss: () -> Unit,
) {
    var textFieldValue by remember { mutableStateOf(TextFieldValue()) }
    val colorScheme = LocalColorScheme.current

    var errorState by remember { mutableStateOf<IBFrameState>(IBFrameState.DEFAULT(colorScheme)) }
    var message by remember { mutableStateOf("") }

    val errorMessage = stringResource(RLocalization.string.vui_long_nhap_ly_do)

    IBankModalReason(
        title = title,
        labelLeft = stringResource(RLocalization.string.huy),
        labelRight = stringResource(RLocalization.string.xac_nhan),
        placeholderReason = stringResource(RLocalization.string.ly_do),
        state = errorState,
        helpTextLeft = message,
        maxLengthText = 210,
        isShowTextCounter = true,
        isRequired = true,
        filters = listOf(
            VietnameseAccentRemoverFilter(),
            RemoveSpecialCharacterFilterCNR()
        ),
        textValue = textFieldValue,
        onDismissRequest = onDismiss,
        isDismissRequestWhenClickButton = false,
        onClickRight = {
            if (textFieldValue.text.isNotEmpty()) {
                onSubmit(textFieldValue.text)
                onDismiss()
            } else {
                errorState = IBFrameState.ERROR(colorScheme)
                message = errorMessage
            }
        },
        onClickLeft = {
            onDismiss()
        },
        onClearTextInput = {
            textFieldValue = TextFieldValue("")
            message = errorMessage
        },
    ) {
        textFieldValue = it
        if (it.text.isNotEmpty()) {
            errorState = IBFrameState.DEFAULT(colorScheme)
            message = ""
        }
    }
}

@Composable
private fun TxnPendingApprovalItem(
    modifier: Modifier,
    txnItem: ModelCheckAble<TransRequestApprovalDMO>,
    onEventListView: ((ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent<TransRequestApprovalDMO, SimpleSearchRuleFilters>) -> Unit)?,
    onEvent: (ManageApprovalRequestsViewEvent) -> Unit,
    onViewDetailItem: (TransRequestApprovalDMO) -> Unit,
) {
    IBankDataCard(
        modifier = modifier,
        showCheckbox = false,
        isChecked = false,
        onCheckedChange = {},
        cardHeader = {
            TxnPendingApprovalItemHeader(
                txnItem = txnItem,
                onEvent = onEvent,
                onEventListView = onEventListView,
            )
        },
        cardContent = {
            TxnPendingApprovalItemBody(
                txnItem = txnItem,
                onViewDetailItem = {
                    onViewDetailItem(it)
                }
            )
        },
        cardFooter = {

        },
    )
}

@Composable
fun NoDataLayout() {
    val colorScheme = LocalColorScheme.current

    Box(Modifier.fillMaxSize()) {
        IBankEmptyState(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = IBSpacing.spacing3xl),
            verticalArrangement = Arrangement.Top,
            emptyStateType = EmptyStateType.EmptyBox,
            backgroundColor = colorScheme.bgMainSecondary,
            supportingText = stringResource(RLocalization.string.khong_co_du_lieu),
        )
    }
}

@Composable
private fun SearchBox(
    modifier: Modifier,
    onSearchValueChange: (String) -> Unit = {},
) {
    var inputValue by remember { mutableStateOf(TextFieldValue("")) }

    IBankInputSearchBase(
        modifier = modifier,
        placeHolderText = stringResource(id = vn.com.bidv.localization.R.string.tim_kiem),
        textValue = inputValue.text,
        onTextChange = {
            inputValue = TextFieldValue(it)
        }, onRequestChange = {
            onSearchValueChange.invoke(inputValue.text)
        }, onClickClear = {
            inputValue = TextFieldValue("")
        },
        onFocusChange = {}
    )
}

private fun getItemsCheckedCount(list: List<ModelCheckAble<TransRequestApprovalDMO>>): Int {
    return list.count { it.isChecked }
}
