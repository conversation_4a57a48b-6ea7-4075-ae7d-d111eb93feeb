package vn.com.bidv.feature.login.ui.setupsmartotp

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.material3.minimumInteractiveComponentSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.common.extenstion.isNotNullOrEmpty
import vn.com.bidv.common.utils.CollectSideEffect
import vn.com.bidv.common.utils.unpackV2
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.feedback.inlinemessage.InlineMessage
import vn.com.bidv.designsystem.component.feedback.inlinemessage.InlineMessageStatus
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.component.feedback.otpview.IBankOtpItemInputList
import vn.com.bidv.designsystem.component.keyboard.KeyInput
import vn.com.bidv.designsystem.component.keyboard.NumpadKeyboard
import vn.com.bidv.designsystem.component.keyboard.NumpadType
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.designsystem.ui.extension.noRippleClickable
import vn.com.bidv.feature.common.domain.data.ModelRequestSmartOtp
import vn.com.bidv.feature.common.domain.data.SmartOtpDMO
import vn.com.bidv.feature.common.domain.data.SmartOtpStatus
import vn.com.bidv.feature.common.domain.data.UserActiveSmartOtpDMO
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.navigation.NavigationHelper
import vn.com.bidv.feature.login.ui.setupsmartotp.SetupSmartOtpReducer.SetupSmartOtpViewEffect.ActiveSmartOtpSuccessEffect
import vn.com.bidv.feature.login.ui.setupsmartotp.SetupSmartOtpReducer.SetupSmartOtpViewEffect.DeleteSmartOtpSuccessEffect
import vn.com.bidv.feature.login.ui.setupsmartotp.SetupSmartOtpReducer.SetupSmartOtpViewEffect.GetSmartOtpInfoError
import vn.com.bidv.feature.login.ui.setupsmartotp.SetupSmartOtpReducer.SetupSmartOtpViewEffect.GoToSmsPopupActiveSmartOtpEffect
import vn.com.bidv.feature.login.ui.setupsmartotp.SetupSmartOtpReducer.SetupSmartOtpViewEffect.InitSuccessEffect
import vn.com.bidv.feature.login.ui.setupsmartotp.SetupSmartOtpReducer.SetupSmartOtpViewEffect.RequestChangePinSuccessEffect
import vn.com.bidv.feature.login.ui.setupsmartotp.SetupSmartOtpReducer.SetupSmartOtpViewEffect.ShowPopupActivationDifferentUserSmartOtpErrorEffect
import vn.com.bidv.feature.login.ui.setupsmartotp.SetupSmartOtpReducer.SetupSmartOtpViewEffect.ShowPopupCommonErrorEffect
import vn.com.bidv.feature.login.ui.setupsmartotp.SetupSmartOtpReducer.SetupSmartOtpViewEffect.ShowPopupDefaultErrorEffect
import vn.com.bidv.feature.login.ui.setupsmartotp.SetupSmartOtpReducer.SetupSmartOtpViewEffect.ShowPopupSmartOPTWarningEffect
import vn.com.bidv.feature.login.ui.setupsmartotp.SetupSmartOtpReducer.SetupSmartOtpViewEffect.UpdateUserActiveSuccessEffect
import vn.com.bidv.feature.login.ui.setupsmartotp.SetupSmartOtpReducer.SetupSmartOtpViewEvent
import vn.com.bidv.feature.login.ui.setupsmartotp.SetupSmartOtpReducer.SetupSmartOtpViewEvent.DeleteEvent
import vn.com.bidv.feature.login.ui.setupsmartotp.SetupSmartOtpReducer.SetupSmartOtpViewEvent.InputPINChangedEvent
import vn.com.bidv.feature.login.ui.setupsmartotp.SetupSmartOtpReducer.SetupSmartOtpViewState
import vn.com.bidv.localization.R.string
import vn.com.bidv.log.BLogUtil

fun generateSupportText(navController: NavHostController, smartOtpDMO: SmartOtpDMO?): String {
    return when {
        smartOtpDMO?.differentIdNumExists == true ->
            navController.context.getString(string.thiet_bi_da_kich_hoat_smart_otp_cua_nguoi_dung_khac_quy_khach_co_muon_xoa_cai_dat_smart_otp_cua_nguoi_dung_nay_tren_thiet_bi)

        smartOtpDMO?.isNeedRReActiveSmartOtp == true ->
            navController.context.getString(string.quy_khach_vui_long_lien_he_hotline_19009248_de_xac_thuc_yeu_cau_kich_hoat_lai_smart_otp)

        smartOtpDMO?.activationLockTime != null ->
            navController.context.getString(string.quy_khach_da_yeu_cau_doi_thiet_bi_cai_dat_smart_otp_online_de_dam_bao_an_toan_tai_khoan_cua_quy_khach_vui_long_thuc_hien_kich_hoat_lai_sau_thoi_diem) +
                    smartOtpDMO.activationLockTime

        else -> ""
    }
}

@SuppressLint("StateFlowValueCalledInComposition")
@Composable
fun SetupSmartOtpScreen(
    navController: NavHostController,
    data: ModelRequestSmartOtp
) {

    val vm: SetupSmartOtpViewModel = hiltViewModel()
    val (_uiState, _onEvent,_) = vm.unpackV2()
    val supportText = rememberSaveable { mutableStateOf("") }

    val commonErrorMessage = remember { mutableStateOf("") }
    val defaultErrorMessage = remember { mutableStateOf("") }
    val activationDifferentUserSmartOtpErrorMessage = remember { mutableStateOf("") }
    val smartOtpStatus = data.status
    val isDeleteSmartOtpSuccess = remember { mutableStateOf(false) }
    val smartOtpInfoErrorMessage = remember { mutableStateOf("") }

    if (smartOtpInfoErrorMessage.value.isNotNullOrEmpty()) {
        IBankModalConfirm(
            modalConfirmType = ModalConfirmType.Error,
            title = stringResource(string.loi),
            supportingText = smartOtpInfoErrorMessage.value,
            listDialogButtonInfo = listOf(
                DialogButtonInfo(
                    label = stringResource(string.close),
                )
            ),
            onDismissRequest = {
                smartOtpInfoErrorMessage.value = ""
                navController.popBackStack()
            }
        )
    }

    if (commonErrorMessage.value.isNotEmpty()) {
        IBankModalConfirm(
            title = stringResource(string.loi),
            modalConfirmType = ModalConfirmType.Error,
            supportingText = commonErrorMessage.value,
            listDialogButtonInfo = listOf(
                DialogButtonInfo(
                    label = stringResource(string.close),
                )
            ),
            onDismissRequest = {
                commonErrorMessage.value = ""
                NavigationHelper.navigateFromActiveSmartOtpToHomeScreen(navController)
            }
        )
    }

    if (defaultErrorMessage.value.isNotEmpty()) {
        IBankModalConfirm(
            title = stringResource(string.loi),
            modalConfirmType = ModalConfirmType.Error,
            supportingText = defaultErrorMessage.value,
            listDialogButtonInfo = listOf(
                DialogButtonInfo(
                    label = stringResource(string.close),
                )
            ),
            onDismissRequest = {
                defaultErrorMessage.value = ""
            }
        )
    }

    if (activationDifferentUserSmartOtpErrorMessage.value.isNotEmpty()) {
        IBankModalConfirm(
            supportingText = activationDifferentUserSmartOtpErrorMessage.value,
            modalConfirmType = ModalConfirmType.Warning,
            listDialogButtonInfo = listOf(
                DialogButtonInfo(
                    label = stringResource(string.yes),
                    isDismissRequest = false,
                    onClick = {
                        activationDifferentUserSmartOtpErrorMessage.value = ""
                        _onEvent(
                            SetupSmartOtpViewEvent.DeleteSmartOtpEvent
                        )
                    }
                ),
                DialogButtonInfo(
                    label = stringResource(string.de_sau),
                ),
            ),
            onDismissRequest = {
                activationDifferentUserSmartOtpErrorMessage.value = ""
                NavigationHelper.navigateFromActiveSmartOtpToHomeScreen(navController)
            }
        )

    }

    if (supportText.value.isNotEmpty()) {
        val dialogButtons = mutableListOf<DialogButtonInfo>()

        if (_uiState.value.modelRequestSmartOtp?.smartOtp?.differentIdNumExists == true) {
            dialogButtons.add(
                DialogButtonInfo(
                    label = stringResource(string.yes),
                    isDismissRequest = false,
                    onClick = {
                        supportText.value = ""
                        _onEvent(SetupSmartOtpViewEvent.DeleteSmartOtpEvent)
                    }
                )
            )
        }

        dialogButtons.add(
            DialogButtonInfo(
                label = stringResource(string.de_sau),
            )
        )

        IBankModalConfirm(
            title = stringResource(string.canh_bao),
            modalConfirmType = ModalConfirmType.Warning,
            supportingText = supportText.value,
            listDialogButtonInfo = dialogButtons,
            onDismissRequest = {
                supportText.value = ""
                NavigationHelper.navigateToUserInfoScreen(navController)
            }
        )
    }

    if (isDeleteSmartOtpSuccess.value) {
        if (_uiState.value.inputPIN.isNotEmpty() && _uiState.value.confirmPIN.isNotEmpty()) {
            _onEvent(SetupSmartOtpViewEvent.ValidateInputEvent)
        } else {
            val smartOtpDMO = _uiState.value.modelRequestSmartOtp?.smartOtp?.copy(differentIdNumExists = false)
            _onEvent(SetupSmartOtpViewEvent.OnUpdateSmartOtpInfo(smartOtpDMO))
            supportText.value = generateSupportText(navController, smartOtpDMO)
        }
        isDeleteSmartOtpSuccess.value = false
    }

    CollectSideEffect(vm.subscribeShareData(Constants.CHANGE_PIN_SUCCESS)) {
        if (it.data == Constants.CHANGE_PIN_SUCCESS) {
            data.userActiveSmartOtpDMO?.copy(
                p = vm.uiState.value.confirmPIN,
                ts = System.currentTimeMillis().toString()
            )?.let { user ->
                _onEvent(SetupSmartOtpViewEvent.UpdateUserActiveEvent(user))
            }
        }
    }
    CollectSideEffect(vm.subscribeShareData(Constants.CLEAR_INPUT_PIN)) {
        _onEvent(SetupSmartOtpViewEvent.ClearDataInputPinEvent)
    }
    CollectSideEffect(vm.subscribeShareData(Constants.NEED_DELETE_ALL_SMART_OTP_SUCCESS)) {
        if (it.key == Constants.NEED_DELETE_ALL_SMART_OTP_SUCCESS) {
            _onEvent(SetupSmartOtpViewEvent.DeleteSmartOtpEvent)
        }
    }

    BaseScreen(
        navController = navController,
        viewModel = vm,
        backgroundColor = LocalColorScheme.current.bgMainTertiary,
        topAppBarType = TopAppBarType.Title,
        topAppBarConfig = TopAppBarConfig(
            isShowActionItem = false,
            titleTopAppBar = if (smartOtpStatus == SmartOtpStatus.CHANGE_PIN) stringResource(string.doi_pin_smart_otp) else stringResource(
                string.kich_hoat_smart_otp
            ),
            showHomeIcon = smartOtpStatus == SmartOtpStatus.CHANGE_PIN,
            isShowNavigationIcon = smartOtpStatus == SmartOtpStatus.CHANGE_PIN
        ),
        renderContent = { uiState, onEvent ->
            if (!vm.uiState.value.isInit) {
                onEvent(SetupSmartOtpViewEvent.OnInitEvent(modelRequestSmartOtp = data))
            } else {
                Content(
                    uiState = uiState,
                    onEvent = onEvent,
                    status = smartOtpStatus,
                    navController = navController
                )
            }

        },

        handleSideEffect = { sideEffect ->

            BLogUtil.d( "handleSideEffect: $sideEffect")

            when (sideEffect) {

                is InitSuccessEffect -> {
                    val smartOtpDMO = _uiState.value.modelRequestSmartOtp?.smartOtp

                    supportText.value = generateSupportText(navController, smartOtpDMO)
                }

                is ShowPopupSmartOPTWarningEffect -> {
                    supportText.value = ""
                }
                is ShowPopupCommonErrorEffect -> {
                    commonErrorMessage.value = sideEffect.errorMessage
                }
                is ShowPopupDefaultErrorEffect -> {
                    defaultErrorMessage.value = sideEffect.errorMessage
                }
                is ShowPopupActivationDifferentUserSmartOtpErrorEffect -> {
                    activationDifferentUserSmartOtpErrorMessage.value = sideEffect.errorMessage
                }
                is GoToSmsPopupActiveSmartOtpEffect -> {
                    NavigationHelper.navigateToActiveSmartOTP(
                        navController,
                        sideEffect.data.copy(
                            pinCode = sideEffect.data.pinCode,
                            userId = _uiState.value.userInfo?.userId.toString(),
                            cifName = _uiState.value.userInfo?.cifName,
                            username = _uiState.value.userInfo?.username,
                        )
                    )
                }
                is ActiveSmartOtpSuccessEffect -> {
                    val result = sideEffect.data
                    vm.saveUserActiveOtp(
                        userActiveSmartOtpDMO = UserActiveSmartOtpDMO(
                            u = _uiState.value.userInfo?.userId?.toString() ?: "",
                            p = result.pinCode ?: "",
                            s = result.secretKey ?: "",
                            st = result.smToken ?: "",
                            c = _uiState.value.userInfo?.cifName ?: "",
                            un = _uiState.value.userInfo?.username ?: "",
                            ts = System.currentTimeMillis().toString()
                        )
                    )
                    NavigationHelper.navigateFromActiveSmartOtpToHomeScreen(navController)
                }

                is DeleteSmartOtpSuccessEffect -> {
                    isDeleteSmartOtpSuccess.value = true
                }
                is RequestChangePinSuccessEffect -> {
                    NavigationHelper.navigateToChangePinSmsOTP(navController, sideEffect.data)
                }
                is UpdateUserActiveSuccessEffect -> {
                    NavigationHelper.navigateToChangePinSuccess(navController)
                }
                is GetSmartOtpInfoError -> {
                    smartOtpInfoErrorMessage.value = sideEffect.errorMessage ?: ""
                }
                else -> {
                    //nothing
                }

            }
        }
    )

}

@Composable
private fun Content(
    status: SmartOtpStatus,
    uiState: SetupSmartOtpViewState,
    navController: NavHostController,
    onEvent: (SetupSmartOtpViewEvent) -> Unit,
) {

    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current
    val isInputFocus = remember { mutableStateOf(true) }
    val isConfirmFocus = remember { mutableStateOf(false) }

    val inputSize = 6
    val isEqualLength =
        uiState.inputPIN.length == inputSize && uiState.confirmPIN.length == inputSize
    val isEqualValue = uiState.inputPIN == uiState.confirmPIN

    val isSameOldPin =
        uiState.inputPIN.length == inputSize && uiState.inputPIN == uiState.modelRequestSmartOtp?.userActiveSmartOtpDMO?.p

    val onKeyClickHandler = rememberUpdatedState { key: KeyInput ->
        when (key) {
            is KeyInput.Delete -> {
                if (isInputFocus.value) {
                    onEvent(DeleteEvent(InputType.INPUT))
                }
                if (isConfirmFocus.value) {
                    onEvent(DeleteEvent(InputType.CONFIRM))
                }
            }

            is KeyInput.Number -> {
                if (isInputFocus.value && uiState.inputPIN.length < inputSize) {
                    onEvent(InputPINChangedEvent(key.value, InputType.INPUT))
                }
                if (isConfirmFocus.value && uiState.confirmPIN.length < inputSize) {
                    onEvent(InputPINChangedEvent(key.value, InputType.CONFIRM))
                }
            }

            else -> {
                // no action
            }
        }
    }
    Box {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .align(Alignment.BottomCenter)
        ) {
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(horizontal = IBSpacing.spacingM)
                    .verticalScroll(rememberScrollState())
            ) {
                if (status == SmartOtpStatus.ACTIVE) {
                    Spacer(modifier = Modifier.height(IBSpacing.spacingS))
                    InlineMessage(
                        status = InlineMessageStatus.Brand(LocalColorScheme.current),
                        message = stringResource(string.quy_khach_cai_dat_ma_pin_smart_otp_de_su_dung_khi_xac_thuc_giao_dich) + "\n" + stringResource(
                            string.vui_long_ghi_nho_va_khong_tiet_lo_ma_pin_cho_bat_ky_ai
                        ),
                    )
                }
                Spacer(modifier = Modifier.height(IBSpacing.spacingS))

                InputPINView(
                    inputSize = inputSize,
                    title = if (status == SmartOtpStatus.CHANGE_PIN) stringResource(string.nhap_ma_pin_moi)
                    else stringResource(string.nhap_ma_pin),
                    isFocus = isInputFocus.value,
                    text = uiState.inputPIN,
                    onOtpFocus = { isFocus ->
                        isInputFocus.value = isFocus
                        if (isFocus) {
                            isConfirmFocus.value = false
                        }
                    },
                    onOtpInputDone = {
                        if (isEqualLength && isEqualValue && !isSameOldPin) {
                            onEvent(SetupSmartOtpViewEvent.ValidateInputEvent)
                        } else {
                            isInputFocus.value = false
                            isConfirmFocus.value = true
                        }
                    }

                )

                if (!isInputFocus.value && uiState.inputPIN.length in 1..<inputSize) {
                    Spacer(modifier = Modifier.height(IBSpacing.spacingS))
                    Text(
                        stringResource(string.ma_pin_gom_6_ky_tu),
                        style = typography.labelLabel_m,
                        color = colorScheme.contentNegativeSecondary,
                        textAlign = TextAlign.Start,
                        modifier = Modifier.align(Alignment.Start)
                    )
                }

                if (isSameOldPin) {
                    Spacer(modifier = Modifier.height(IBSpacing.spacingS))
                    Text(
                        stringResource(string.ma_pin_moi_khong_duoc_trung_ma_pin_cu),
                        style = typography.labelLabel_m,
                        color = colorScheme.contentNegativeSecondary,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.align(Alignment.CenterHorizontally)
                    )
                }

                Spacer(modifier = Modifier.height(IBSpacing.spacingL))

                InputPINView(
                    inputSize = inputSize,
                    title = stringResource(string.xac_nhan_lai_ma_pin),
                    isFocus = isConfirmFocus.value,
                    text = uiState.confirmPIN,
                    onOtpFocus = { isFocus ->
                        isConfirmFocus.value = isFocus
                        if (isFocus) {
                            isInputFocus.value = false
                        }

                    },
                    onOtpInputDone = {
                        if (isEqualLength && isEqualValue && !isSameOldPin) {
                            onEvent(SetupSmartOtpViewEvent.ValidateInputEvent)
                        }
                    }
                )

                if (!isConfirmFocus.value && uiState.confirmPIN.length in 1..<inputSize) {
                    Spacer(modifier = Modifier.height(IBSpacing.spacingS))
                    Text(
                        stringResource(string.ma_pin_gom_6_ky_tu),
                        style = typography.labelLabel_m,
                        color = colorScheme.contentNegativeSecondary,
                        textAlign = TextAlign.Start,
                        modifier = Modifier.align(Alignment.Start)
                    )
                }



                if (isEqualLength && !isEqualValue) {
                    Spacer(modifier = Modifier.height(IBSpacing.spacingS))
                    Text(
                        stringResource(string.ma_pin_va_xac_nhan_lai_ma_pin_khong_trung_nhau),
                        style = typography.labelLabel_m,
                        color = colorScheme.contentNegativeSecondary,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.align(Alignment.CenterHorizontally)
                    )
                }

                if (status == SmartOtpStatus.ACTIVE) {
                    Text(
                        stringResource(string.kich_hoat_sau),
                        style = typography.labelLabel_l,
                        color = colorScheme.contentBrand_01Primary,
                        textAlign = TextAlign.Center,
                        modifier = Modifier
                            .align(Alignment.CenterHorizontally)
                            .noRippleClickable {
                                NavigationHelper.navigateFromActiveSmartOtpToHomeScreen(navController)
                            }
                            .then(Modifier.minimumInteractiveComponentSize())
                    )
                }

                Spacer(modifier = Modifier.weight(1f))
            }

            NumpadKeyboard(
                type = NumpadType.SHUFFLED,
                onKeyClick = { key -> onKeyClickHandler.value(key) })

        }
    }
}

@Composable
private fun InputPINView(
    inputSize: Int,
    title: String,
    isFocus: Boolean = false,
    text: String,
    onOtpFocus: (Boolean) -> Unit = {},
    onOtpInputDone: (String) -> Unit = {},

    ) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current

    var inputVisibility by remember { mutableStateOf(false) }
    val icon = if (inputVisibility) {
        R.drawable.eyes_closed_outline
    } else R.drawable.eyes_open_outline


    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically

        ) {
            Text(
                title,
                style = typography.titleTitle_s,
                color = colorScheme.contentMainPrimary,
            )

            Image(
                painter = painterResource(id = icon),
                contentDescription = null,
                modifier = Modifier
                    .padding(start = 4.dp)
                    .size(20.dp)
                    .clickable {
                        inputVisibility = !inputVisibility
                    }
            )

        }

        Spacer(modifier = Modifier.height(IBSpacing.spacingM))

        IBankOtpItemInputList(
            modifier = Modifier
                .fillMaxWidth()
                .clickable(indication = null, interactionSource = remember {
                    MutableInteractionSource()
                }) {
                    onOtpFocus(true)
                },
            otpItemNumber = inputSize,
            otpText = if (text.isNotEmpty() && !inputVisibility) "*".repeat(text.length) else text,
            isFocus = isFocus,
            onOtpInputDone = {
                onOtpInputDone(text)
            }

        )

    }
}




