#!/bin/bash
# <PERSON>ript to copy modules from source project to target project

# Print colored status messages
print_status() {
  local color="\033[0;36m"  # Cyan color
  local reset="\033[0m"     # Reset color
  echo -e "${color}==>${reset} $1"
}

# Error handling function
handle_error() {
  local error_msg="$1"
  echo -e "\033[0;31mERROR:\033[0m $error_msg"
  exit 1
}

# Define source project path (to be provided as argument or set manually)
if [ -z "$1" ]; then
  # No argument provided, ask user for source path
  read -p "Enter the source project path: " SOURCE_REPO
else
  SOURCE_REPO="$1"
fi

# Verify source path exists
if [ ! -d "$SOURCE_REPO" ]; then
  handle_error "Source repository directory does not exist: $SOURCE_REPO"
fi

# Current directory is the target
TARGET_REPO=$(pwd)

print_status "Starting copy from source project to target project..."
print_status "Source: $SOURCE_REPO"
print_status "Target: $TARGET_REPO"

# Define the specific modules to copy
MODULES=(
  "core/public"
  "repository"
  "feature/login"
  "feature/common"
  "feature/homepage"
)

# Process each defined module
for module_path in "${MODULES[@]}"; do
  source_module_path="$SOURCE_REPO/$module_path"
  target_module_path="$TARGET_REPO/$module_path"
  
  # Check if source directory exists
  if [ ! -d "$source_module_path" ]; then
    print_status "WARNING: Source directory doesn't exist: $source_module_path"
    print_status "Skipping module: $module_path"
    continue
  fi
  
  # Check if target directory exists, create if not
  if [ ! -d "$target_module_path" ]; then
    print_status "Creating target directory: $target_module_path"
    mkdir -p "$target_module_path" || handle_error "Failed to create directory: $target_module_path"
  fi
  
  print_status "Copying $module_path from source to target..."
  
  # Copy recursively, preserving attributes
  # Using rsync if available for more efficient copying
  if command -v rsync &> /dev/null; then
    rsync -av --delete "$source_module_path/" "$target_module_path/" || handle_error "Failed to rsync $module_path"
  else
    # Fallback to cp if rsync is not available
    rm -rf "$target_module_path"/* || handle_error "Failed to clean target directory: $target_module_path"
    cp -R "$source_module_path"/* "$target_module_path"/ || handle_error "Failed to copy $module_path"
  fi
  
  print_status "Successfully copied $module_path from source to target"
done

print_status "All modules have been successfully copied from source to target repository!"
print_status "Source: $SOURCE_REPO"
print_status "Target: $TARGET_REPO"