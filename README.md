# Ibank Android 2.0

## Documentation
  * [**Getting started**](document/GettingStarted.md): Outlines the process for using Git Flow in the iBank 2.
  * [**Coding Structure**](document/CodingStructure.md): Guide to implementing Clean Architecture into project, Coding convention.
  * [**Open API Generator Setup**](document/OpenApiGenerator.md): Set up Open API generator to generate API client code(ApiClass, RequestClass, ResponseClass) from the Open API specification.
  * [**Template setup**](document/TemplateSetup.md): Set up custom file templates for generating MVI components such as `MVITemplate`
  * [**Publish Library**](document/PublishLibrary.md): Publish library to local repository
  * [**Publish Plugin**](document/PublishPlugin.md): Publish custom plugin to local repository
  * [**New feature module navigation**](document/RegisterFeatureModuleNavigation.md): Register any public routes to main navigation graph
  * [**UI component GenAI template**](document/UIComponentGeneratePromtTemplate.md): Prompt template to generate component UI code by AI
  * [**Project Coding Standards**](https://docs.google.com/document/d/1BzxGn6VdaACHLqddwB-Im7LOw5C1z9rT/edit#heading=h.kc0kxzxgglfi): Required coding practices for all team members. They ensure code consistency, quality, and ease of  collaboration.
  * [**Pull Request Rule**](document/PullRequestRule.md): Guide to create pull request.