package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.tabcontent.pendingtransaction.advsearch

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.feature.common.utils.ModalConfirmConfig
import vn.com.bidv.feature.government.service.data.GovernmentServiceUseCase
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class AdvSearchViewModel @Inject constructor(
    private val governmentServiceUseCase: GovernmentServiceUseCase
) :
    ViewModelIBankBase<AdvSearchReducer.AdvSearchState, AdvSearchReducer.AdvSearchEvent, AdvSearchReducer.AdvSearchEffect>(
        reducer = AdvSearchReducer(),
        initialState = AdvSearchReducer.AdvSearchState()
    ) {
    override fun handleEffect(
        sideEffect: AdvSearchReducer.AdvSearchEffect,
        onResult: (AdvSearchReducer.AdvSearchEvent) -> Unit
    ) {
        when (sideEffect) {
            AdvSearchReducer.AdvSearchEffect.FetCurrencyList ->
                callDomain(showLoadingIndicator = false,
                    isListenAllError = true,
                    onSuccess = { item ->
                        onResult(AdvSearchReducer.AdvSearchEvent.GetCurrencyListSuccess(listCurrency = item.data ?: listOf("VND")))
                    },
                    onFail = {
                        onResult(
                            AdvSearchReducer.AdvSearchEvent.ShowError(
                                modalConfirm = ModalConfirmConfig(
                                    modalConfirmType = ModalConfirmType.Error,
                                    title = resourceProvider.getString(R.string.loi),
                                    supportingText = it?.errorMessage ?: ""
                                )
                            )
                        )
                    }
                ) {
                    governmentServiceUseCase.getListCurrency()
                }

            AdvSearchReducer.AdvSearchEffect.FetStatusList ->
                callDomain(showLoadingIndicator = false,
                    isListenAllError = true,
                    onSuccess = { item ->
                        onResult(AdvSearchReducer.AdvSearchEvent.GetStatusListSuccess(listStatus = item.data))
                    },
                    onFail = {
                        onResult(
                            AdvSearchReducer.AdvSearchEvent.ShowError(
                                modalConfirm = ModalConfirmConfig(
                                    modalConfirmType = ModalConfirmType.Error,
                                    title = resourceProvider.getString(R.string.loi),
                                    supportingText = it?.errorMessage ?: ""
                                )
                            )
                        )
                    }
                ) {
                    governmentServiceUseCase.getListStatus()
                }
        }
    }
}

