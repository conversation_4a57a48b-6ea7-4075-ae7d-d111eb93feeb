package vn.com.bidv.feature.government.service.navigation

import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import androidx.navigation.navigation
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.ListCustomsDutiesMainScreen
import vn.com.bidv.feature.government.service.ui.screen.main.GovernmentServiceMainScreen
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.tabcontent.pendingtransaction.pendingtransactiondetail.PendingTransactionDetailScreen
import vn.com.bidv.sdkbase.navigation.FeatureGraphBuilder
import vn.com.bidv.sdkbase.navigation.IBankMainRouting
import javax.inject.Inject

sealed class GovernmentServiceRoute(val route: String) {
    data object GovernmentServiceMainRoute : GovernmentServiceRoute(
        route = "government_service_main_route",
    )
    data object PendingTransactionDetailRoute : GovernmentServiceRoute(
        route = "pending_transaction_detail_route/{transactionId}",
    ) {
        fun createRoute(transactionId: String): String {
            return "pending_transaction_detail_route/$transactionId"
        }
    }

    data object PendingTransactionEditRoute : GovernmentServiceRoute(
        route = "edit_pending_transaction/{transactionId}"
    ) {
        fun createRoute(transactionId: String): String {
            return "edit_pending_transaction/$transactionId"
        }
    }
}

class GovernmentServiceNavigation @Inject constructor() : FeatureGraphBuilder {

    override fun buildGraph(
        navGraphBuilder: NavGraphBuilder,
        navController: NavHostController,
        registeredRoutes: (args: List<String>) -> Unit
    ) {
        navGraphBuilder.navigation(
            startDestination = GovernmentServiceRoute.GovernmentServiceMainRoute.route,
            route = IBankMainRouting.GovernmentServiceRoute.GovernmentServiceMainRoute.route
        ) {
            composable(
                route = GovernmentServiceRoute.GovernmentServiceMainRoute.route
            ) {
                ListCustomsDutiesMainScreen(navController)
            }

            // Add composable for pending transaction detail
            composable(
                route = GovernmentServiceRoute.PendingTransactionDetailRoute.route,
                arguments = listOf(
                    navArgument("transactionId") { type = NavType.StringType }
                )
            ) { backStackEntry ->
                val transactionId = backStackEntry.arguments?.getString("transactionId") ?: ""
                PendingTransactionDetailScreen(
                    transactionId = transactionId,
                    navController = navController
                )
            }
        }

        // Update registered routes to include the new route
        registeredRoutes(
            listOf(
                IBankMainRouting.GovernmentServiceRoute.GovernmentServiceMainRoute.route
            )
        )
    }
}
