package vn.com.bidv.feature.government.service.ui.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.dataentry.IBankInformation
import vn.com.bidv.designsystem.component.navigation.button.IBankNormalButton
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonType
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.model.ActionType

data class FailedApiCallPayload(
    val error: FailedApiCallBinding? = null,
)

enum class FailedApiCallBinding(val code: String, val message: String) {
    SUCCESS("0", "Thành công"),
    GOV0002("GOV0002", "Có lỗi xảy ra trong quá trình xử lý. Vui lòng thử lại."),
    GOV0008("GOV0008", "Không tìm thấy giao dịch. Vui lòng kiểm tra lại."),
    GOV0010("GOV0010", "Thông tin và trạng thái giao dịch không hợp lệ để xử lý yêu cầu. Vui lòng kiểm tra lại"),
    GOV0011("GOV0011", "Thông tin giao dịch không hợp lệ để xử lý. Vui lòng kiểm tra lại.")
}

@Composable
fun FailedApiCallDialog(
    modifier: Modifier = Modifier,
    payload: FailedApiCallPayload,
    onDismissRequest: () -> Unit,
) {
    val colorScheme = LocalColorScheme.current
    val screenHeight = LocalConfiguration.current.screenHeightDp.dp

    Dialog(onDismissRequest = onDismissRequest) {
        Box(
            modifier = modifier.heightIn(max = screenHeight * 0.9606f),
            contentAlignment = Alignment.TopEnd
        ) {
            Surface(
                shape = RoundedCornerShape(IBSpacing.spacingL),
                color = colorScheme.contentOn_specialPrimary
            ) {
                IconButton(
                    modifier = Modifier.align(Alignment.TopEnd),
                    onClick = onDismissRequest
                ) {
                    Icon(
                        painter = painterResource(R.drawable.modal_confirm_close),
                        contentDescription = null
                    )
                }
                Column(modifier = Modifier.fillMaxWidth().padding(16.dp)) {
                    val (icon, message) = if (payload.error == null) {
                        Pair(
                            painterResource(id = R.drawable.icon_popup_success_64),
                            stringResource(
                                vn.com.bidv.localization.R.string.xoa_thanh_cong
                            )
                        )
                    } else {
                        Pair(
                            painterResource(id = R.drawable.icon_popup_error_64),
                            stringResource(
                                vn.com.bidv.localization.R.string.xoa_khong_thanh_cong
                            )
                        )
                    }

                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = IBSpacing.spacingM),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {

                        Icon(
                            painter = icon,
                            contentDescription = null,
                            tint = Color.Unspecified,
                            modifier = Modifier.size(64.dp)
                        )
                        Spacer(modifier = Modifier.size(IBSpacing.spacingXs))
                        Text(
                            modifier = Modifier.fillMaxWidth(),
                            text = message,
                            style = LocalTypography.current.bodyBody_l,
                            color = LocalColorScheme.current.contentMainPrimary,
                            textAlign = TextAlign.Center
                        )
                    }
                    Spacer(modifier = Modifier.size(IBSpacing.spacingM))
                    SingleRequestContent(payload)
                    IBankNormalButton(
                        modifier = Modifier.fillMaxWidth().padding(top = 16.dp),
                        type = NormalButtonType.PRIMARY(colorScheme),
                        text = stringResource(vn.com.bidv.localization.R.string.xac_nhan)
                    ) {
                        onDismissRequest()
                    }
                }
            }
        }
    }
}

@Composable
fun SingleRequestContent(
    payload: FailedApiCallPayload
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(IBSpacing.spacingXs))
                .background(LocalColorScheme.current.bgMainPrimary)
                .padding(horizontal = IBSpacing.spacingM, vertical = IBSpacing.spacingS),
        ) {
            payload.error?.message?.let {
                IBankInformation(
                    modifier = Modifier.fillMaxWidth(),
                    label = stringResource(vn.com.bidv.localization.R.string.mo_ta_loi),
                    dataValue = it
                )
            }
        }
    }
}

@Preview
@Composable
fun SingleRequestContentPreview() {
    SingleRequestContent(
        payload = FailedApiCallPayload(
            error = FailedApiCallBinding.GOV0002
        )
    )
}

@Preview
@Composable
fun FailedApiCallDialogPreview() {
    FailedApiCallDialog(
        payload = FailedApiCallPayload(
            error = FailedApiCallBinding.GOV0002
        ),
        onDismissRequest = {}
    )
}
