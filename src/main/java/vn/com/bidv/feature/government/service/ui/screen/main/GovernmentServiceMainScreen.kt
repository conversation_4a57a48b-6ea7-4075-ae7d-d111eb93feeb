package vn.com.bidv.feature.government.service.ui.screen.main

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.feature.government.service.navigation.GovernmentServiceRoute

@Composable
fun GovernmentServiceMainScreen(navController: NavHostController) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(IBSpacing.spacingM),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text("Government Service Main Screen")
        
        // Development button to navigate to detail screen
        But<PERSON>(
            onClick = {
                // Using a hardcoded transaction ID for development
                val devTransactionId = "DEV123456789"
                navController.navigate(
                    GovernmentServiceRoute.PendingTransactionDetailRoute.createRoute(devTransactionId)
                )
            },
            modifier = Modifier.padding(top = IBSpacing.spacingM)
        ) {
            Text("View Sample Transaction Detail")
        }
    }
}