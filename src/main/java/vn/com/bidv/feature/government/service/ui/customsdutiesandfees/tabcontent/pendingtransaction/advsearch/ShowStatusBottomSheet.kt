package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.tabcontent.pendingtransaction.advsearch

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import vn.com.bidv.designsystem.component.feedback.bottomsheet.IBankMultiChoiceSearchDialog
import vn.com.bidv.designsystem.component.feedback.bottomsheet.ItemMultiSelect
import vn.com.bidv.designsystem.component.feedback.bottomsheet.MultiSearchDialogState
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonType
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankActionBar
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.TransactionStatusBase

@Composable
fun ShowStatusBottomSheet(
    uiState: AdvSearchReducer.AdvSearchState,
    showStatusBottomSheet: MutableState<Boolean>,
    data: List<TransactionStatusBase>?,
    state: MultiSearchDialogState,
    onItemSelected: (List<TransactionStatusBase>) -> Unit,
) {
    if (showStatusBottomSheet.value) {
        val colorScheme = LocalColorScheme.current
        var listStatusSelected by remember {
            mutableStateOf(
                uiState.listStatusSelected ?: emptyList()
            )
        }

        LaunchedEffect(uiState.listStatusSelected) {
            listStatusSelected = uiState.listStatusSelected ?: emptyList()
        }

        val totalSelected = listStatusSelected.size
        val totalItems = data?.size ?: 0
        val isAllSelected = totalSelected == totalItems
        val isPartialSelected = totalSelected in 1 until totalItems

        IBankMultiChoiceSearchDialog(title = stringResource(R.string.trang_thai),
            searchPlaceholder = stringResource(R.string.tim_kiem),
            listData = data,
            showSearchBox = true,
            itemSelected = listStatusSelected.ifEmpty { data },
            isSelectedAllSupport = true,
            state = state,
            onApply = {
                showStatusBottomSheet.value = false
                it?.let(onItemSelected)
            },
            searchFilter = { status, query ->
                query.isBlank()
            },
            selectedAllView = { _, click ->
                Column {
                    ItemMultiSelect(
                        isSelected = isAllSelected || isPartialSelected,
                        indeterminate = isPartialSelected,
                        content = stringResource(id = R.string.tat_ca)
                    ) {
                        listStatusSelected = if (isAllSelected) {
                            emptyList()
                        } else {
                            data ?: emptyList()
                        }
                        click.invoke()
                    }
                    HorizontalDivider(
                        modifier = Modifier.padding(
                            bottom = IBSpacing.spacing2xs, top = IBSpacing.spacingXs
                        ),
                        thickness = IBBorderDivider.borderDividerS,
                        color = colorScheme.borderMainSecondary
                    )
                }
            },
            applyView = { click ->
                IBankActionBar(isVertical = false,
                    buttonNegative = DialogButtonInfo(stringResource(R.string.huy)) {
                        showStatusBottomSheet.value = false
                    },
                    typeNegativeButton = NormalButtonType.SECONDARYGRAY(colorScheme),
                    buttonPositive = DialogButtonInfo(stringResource(R.string.xac_nhan)) {
                        click.invoke()
                        showStatusBottomSheet.value = false
                    }
                )
            },
            itemContent = { _, item, click ->
                ItemMultiSelect(item.isSelected, stringResource(item.data.statusResourceId)) {
                    val updatedList = listStatusSelected.toMutableList()
                    item.data.let {
                        if (item.isSelected) {
                            updatedList.remove(it)
                        } else {
                            updatedList.add(it)
                        }
                    }
                    listStatusSelected = updatedList
                    click.invoke()
                }
            }
        )
    }
}