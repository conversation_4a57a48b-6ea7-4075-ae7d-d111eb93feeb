package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.tabcontent.pendingtransaction.advsearch

import vn.com.bidv.localization.R

sealed class AdvSearchItem(open val titleRes: Int) {
    sealed class DropDownItem(titleRes: Int) : AdvSearchItem(titleRes) {
        class Status(titleRes: Int = R.string.trang_thai) : DropDownItem(titleRes)
        class Currency(titleRes: Int = R.string.loai_tien) : DropDownItem(titleRes)
        class ReleaseMethod(titleRes: Int = R.string.phuong_thuc_chuyen_tien) :
            DropDownItem(titleRes)
    }

    data object Date : AdvSearchItem(R.string.ngay_khoi_tao)

    sealed class InputMoney(titleRes: Int) : AdvSearchItem(titleRes) {
        class MinAmount(titleRes: Int = R.string.tong_so_tien_giao_dich_toi_thieu) :
            InputMoney(titleRes)

        class MaxAmount(titleRes: Int = R.string.tong_so_tien_giao_dich_toi_da) :
            InputMoney(titleRes)
    }

    class DebitAccount(titleRes: Int = R.string.tai_khoan_trich_no) : AdvSearchItem(titleRes)
    class TaxId(titleRes: Int = R.string.ma_so_thue_nguoi_nop_thue) : AdvSearchItem(titleRes)
    class DeclarationNumber(titleRes: Int = R.string.nguoi_khoi_tao) :
        AdvSearchItem(titleRes) // todo: chưa có string resource - update sau

    class BatchNumber(titleRes: Int = R.string.so_tham_chieu_lo) : AdvSearchItem(titleRes)
}
