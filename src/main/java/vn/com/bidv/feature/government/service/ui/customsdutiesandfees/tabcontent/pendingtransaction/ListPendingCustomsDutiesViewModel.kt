package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.tabcontent.pendingtransaction

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.ListTransactionBaseReducer
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.ListTransactionBaseViewModel
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnPendingListRes
import vn.com.bidv.feature.government.service.model.ListCustomsDutiesRuleFilter
import javax.inject.Inject

@HiltViewModel
class ListPendingCustomsDutiesViewModel @Inject constructor(
) : ListTransactionBaseViewModel<TxnPendingListRes, ListCustomsDutiesRuleFilter>(
    initialState = ListTransactionBaseReducer.ListTransactionBaseViewState(),
    reducer = ListTransactionBaseReducer()
) {
    override fun handleEffect(
        sideEffect: ListTransactionBaseReducer.ListTransactionBaseViewEffect<TxnPendingListRes>,
        onResult: (ListTransactionBaseReducer.ListTransactionBaseViewEvent<TxnPendingListRes, ListCustomsDutiesRuleFilter>) -> Unit
    ) {
    }
}

