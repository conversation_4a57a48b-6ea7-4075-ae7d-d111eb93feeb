package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.tabcontent.pendingtransaction

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.room.Delete
import timber.log.Timber
import vn.com.bidv.common.ui.BaseMVIScreen
import vn.com.bidv.designsystem.component.datadisplay.badge.IBankBadgeLabel
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelSize
import vn.com.bidv.designsystem.component.dataentry.datacard.IBankDataCard
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.listwithloadmorev2.ListAutoLoadMoreViewModel
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.ItemCardCommon
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.ListTransactionBaseReducer
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.ListTransactionBaseScreen
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.model.ActionType
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnPendingListRes
import vn.com.bidv.feature.government.service.model.ListCustomsDutiesRuleFilter
import vn.com.bidv.feature.government.service.navigation.GovernmentServiceRoute
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.tabcontent.pendingtransaction.advsearch.AdvSearchScreen
import vn.com.bidv.sdkbase.utils.TransactionStatusBase
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants
import vn.com.bidv.sdkbase.utils.exts.dateToString
import vn.com.bidv.sdkbase.utils.exts.toDate
import vn.com.bidv.sdkbase.utils.formatAmount
import vn.com.bidv.localization.R as RLocal

@Composable
fun ListPendingCustomsDutiesScreen(
    navController: NavHostController, lazyListState: LazyListState = rememberLazyListState()
) {
    val viewModel: ListPendingCustomsDutiesViewModel = hiltViewModel()
    val loadViewModel: LoadListPendingCustomsDutiesViewModel = hiltViewModel()
    val operationsViewModel: PendingCustomDutiesOperationsViewModel = hiltViewModel()

    var deleteDialogState by remember { mutableStateOf<DeleteDialogState>(DeleteDialogState.HIDDEN) }
    val viewState by viewModel.uiState.collectAsState()

    BaseMVIScreen(
        viewModel = operationsViewModel,
        handleSideEffect = {
            when (it) {
                is PendingCustomDutiesOperationsReducer.ReducerViewEffect.OnDeleteTxnsSucceed -> {
                    Timber.d("Delete transaction succeed" + it.data.toString())
                }

                is PendingCustomDutiesOperationsReducer.ReducerViewEffect.OnDeleteTxnsFailed -> {
                    Timber.d("Delete transaction failed %s %s", it.errorCode, it.errorMessage)
                }

                else -> {}
            }
        },
        renderContent = { _, _ ->
            BaseMVIScreen(
                viewModel = viewModel,
                renderContent = { uiState, onEvent ->
                    ListPendingTransactionScreenContent(
                        viewModel = viewModel,
                        uiState = uiState,
                        onEvent = onEvent,
                        loadListViewModel = loadViewModel,
                        listState = lazyListState,
                        navController = navController,
                        onDeleteButtonClick = {
                            deleteDialogState = it
                        }
                    )
                },
                handleSideEffect = null,
            )
        }
    )

    if (deleteDialogState != DeleteDialogState.HIDDEN) {
        IBankModalConfirm(
            modalConfirmType = ModalConfirmType.Question,
            title = "Xác nhận xoá",
            supportingText = when (deleteDialogState) {
                is DeleteDialogState.SHOWN_BY_OVERFLOW ->
                    "Quý khách có chắc chắn xoá giao dịch?"
                is DeleteDialogState.SHOWN_BY_ACTION_BAR -> {
                    val checkedCount = viewState.listDataState.count { it.isChecked }
                    "Quý khách có chắc chắn xoá $checkedCount giao dịch"
                }

                else -> ""
            },
            listDialogButtonInfo = listOf(
                DialogButtonInfo(
                    label = "Đồng ý",
                    onClick = {
                        val txnIds = when {
                            deleteDialogState is DeleteDialogState.SHOWN_BY_OVERFLOW -> {
                                listOf(
                                    (deleteDialogState as DeleteDialogState.SHOWN_BY_OVERFLOW).txnId
                                )
                            }

                            else -> viewState.listDataState
                                .filter { it.isChecked }
                                .map { it.data.txnId ?: "" }
                        }
                        operationsViewModel.sendEvent(
                            PendingCustomDutiesOperationsReducer.ReducerViewEvent.UserRequestDeleteTxns(
                                txnIds
                            )
                        )
                        Timber.d("Delete transaction $txnIds")
                    }
                ),
                DialogButtonInfo(
                    label = "Huỷ",
                    onClick = {
                        deleteDialogState = DeleteDialogState.HIDDEN
                    }
                ),
            ),
            onDismissRequest = { deleteDialogState = DeleteDialogState.HIDDEN }
        )
    }
}

sealed class DeleteDialogState {
    data object HIDDEN: DeleteDialogState()
    data class SHOWN_BY_OVERFLOW(val txnId: String): DeleteDialogState()
    data object SHOWN_BY_ACTION_BAR: DeleteDialogState()
}

@Composable
private fun ListPendingTransactionScreenContent(
    viewModel: ListPendingCustomsDutiesViewModel,
    loadListViewModel: ListAutoLoadMoreViewModel<TxnPendingListRes, ListCustomsDutiesRuleFilter>,
    listState: LazyListState = rememberLazyListState(),
    navController: NavHostController,
    uiState: ListTransactionBaseReducer.ListTransactionBaseViewState<TxnPendingListRes, ListCustomsDutiesRuleFilter>,
    onEvent: (ListTransactionBaseReducer.ListTransactionBaseViewEvent<TxnPendingListRes, ListCustomsDutiesRuleFilter>) -> Unit,
    onDeleteButtonClick: (DeleteDialogState) -> Unit,
) {
    ListTransactionBaseScreen(
        viewModel = viewModel,
        navController = navController,
        lazyListState = listState,
        loadListViewModel = loadListViewModel,
        resIdTextTransactionType = RLocal.string.d_giao_dich,
        advSearchContent = {
            AdvSearchScreen(searchRuleFilter = it)
        },
        shouldShowCreateButton = uiState.listDataState.any { it.isChecked },
        listAction = listOf(
            ActionType.Delete, ActionType.Push_Approval, ActionType.Create_Transaction
        ),
        handleAction = { type, _ ->
            when (type) {
                ActionType.Delete -> {
                    onDeleteButtonClick(DeleteDialogState.SHOWN_BY_ACTION_BAR)
                }

                ActionType.Push_Approval -> {
                }

                ActionType.Create_Transaction -> {

                }

                else -> {/*do nothing*/
                }
            }
        },
        ruleFilterDefault = ListCustomsDutiesRuleFilter(),
    ) { model, onEventListView ->
        ItemCardCommon(
            transactionsDMO = model,
            uiState = uiState,
            onEvent = onEvent,
            onClickItem = {
                navController.navigate(
                    GovernmentServiceRoute.PendingTransactionDetailRoute.createRoute(model.data.txnId ?: "")
                )
            },
            navController = navController,
            listAction = listOf(
                ActionType.Push_Approval,
                ActionType.EDIT,
                ActionType.Print_Document,
                ActionType.Delete,
            ),
            handleAction = { type ->
                when (type) {
                    ActionType.Push_Approval -> {

                    }

                    ActionType.EDIT -> {

                    }

                    ActionType.Print_Document -> {

                    }

                    ActionType.Delete -> {
                        onDeleteButtonClick(DeleteDialogState.SHOWN_BY_OVERFLOW(model.data.txnId ?: ""))
                    }

                    else -> {/*do nothing*/
                    }
                }
            },
            contentHeader = { modelHeader ->
                ItemCardHeader(modelHeader.data)
            },
            contentBody = { modelBody ->
                ItemCardBody(modelBody.data)
            },
            onEventListView = onEventListView,
        )
    }
}

@Composable
private fun ItemCardHeader(
    data: TxnPendingListRes
) {
    val style = LocalTypography.current
    val colorScheme = LocalColorScheme.current

    val status = when (data.status) {
        "INIT" -> TransactionStatusBase.INIT
        "REJECTED" -> TransactionStatusBase.REJECTED
        else -> {
            TransactionStatusBase.UNKNOWN
        }
    }

    Row(
        modifier = Modifier
            .padding(horizontal = IBSpacing.spacingXs),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Start
    ) {
        Text(
            modifier = Modifier.weight(1f, fill = false),
            text = data.txnId ?: "",
            style = style.titleTitle_s,
            color = colorScheme.contentMainPrimary,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
        )

        IBankBadgeLabel(
            modifier = Modifier.padding(start = 8.dp),
            title = data.statusName ?: "",
            badgeSize = LabelSize.M,
            badgeColor = status.color
        )
    }

}

@Composable
private fun ItemCardBody(
    item: TxnPendingListRes
) {
    val style = LocalTypography.current
    val colorScheme = LocalColorScheme.current

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                start = IBSpacing.spacingM, end = IBSpacing.spacingM, bottom = IBSpacing.spacingXs
            )
    ) {
        Text(
            modifier = Modifier.fillMaxWidth(),
            text = item.treasuryName ?: "",
            style = style.titleTitle_s,
            color = colorScheme.contentMainPrimary,
        )



        Spacer(modifier = Modifier.height(IBSpacing.spacingS))

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = IBSpacing.spacing3xs),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                modifier = Modifier,
                text = item.treasuryCode ?: "",
                style = style.bodyBody_m,
                color = colorScheme.contentMainTertiary,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            VerticalDivider(
                modifier = Modifier
                    .height(IBSpacing.spacingL)
                    .padding(horizontal = IBSpacing.spacingXs),
                thickness = IBBorderDivider.borderDividerXs,
                color = colorScheme.borderSolidPrimary
            )
            Text(
                modifier = Modifier,
                text = item.treasuryName ?: "",
                style = style.bodyBody_m,
                color = colorScheme.contentMainTertiary,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }

        Spacer(modifier = Modifier.height(IBSpacing.spacingS))

        Row(
            modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                modifier = Modifier.padding(end = IBSpacing.spacing2xs),
                text = item.amount.formatAmount(currCode = "VND"),
                style = style.titleTitle_m,
                color = colorScheme.contentMainPrimary,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            Text(
                modifier = Modifier,
                text = item.ccy ?: "",
                style = style.titleTitle_m,
                color = colorScheme.contentMainPrimary,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            Spacer(modifier = Modifier.width(IBSpacing.spacingS))
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.End,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    modifier = Modifier.size(20.dp),
                    painter = painterResource(id = vn.com.bidv.designsystem.R.drawable.calendar_outline),
                    contentDescription = "",
                    tint = Color.Unspecified
                )
                Spacer(modifier = Modifier.width(IBSpacing.spacing2xs))
                Text(
                    modifier = Modifier,
                    text = item.createdDate
                        ?.toDate(pattern = SdkBaseConstants.DateTimeConstants.INPUT_FORMAT_DD_MM_YYYY_HH_MM_SS_SSSSSS)
                        ?.dateToString() ?: "",
                    style = style.bodyBody_m,
                    color = colorScheme.contentMainTertiary,
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewItemCardHeader() {
    val itemCardContent = TxnPendingListRes(
        txnId = "GD1122GD",
        amount = "***************",
        ccy = "VND",
        treasuryCode = "711",
        treasuryName = "Kho bạc nhà nước Việt Nam",
        createdDate = "30/10/2024",
        status = TransactionStatusBase.REJECTED.statusCode,
        statusName = stringResource(TransactionStatusBase.REJECTED.statusResourceId)
    )

    ItemCardHeader(
        data = itemCardContent
    )
}

@Preview(showBackground = true)
@Composable
private fun PreviewItemCardBody() {
    val data = TxnPendingListRes(
        txnId = "**********************ăddwdwdwdwdwdwdwdwd22**********************22",
        amount = "***************",
        ccy = "VND",
        treasuryCode = "711",
        treasuryName = "Kho bạc nhà nước Việt Nam",
        createdDate = "30/10/2024",
        status = TransactionStatusBase.REJECTED.statusCode,
        statusName = stringResource(TransactionStatusBase.REJECTED.statusResourceId)
    )

    IBankDataCard(
        modifier = Modifier.padding(horizontal = IBSpacing.spacingM),
        showCheckbox = false,
        isChecked = false,
        onCheckedChange = {},
        cardHeader = {
            ItemCardHeader(data)
        },
        cardContent = {
            ItemCardBody(data)
        },
        cardFooter = {},
    )
}

@Preview(showBackground = true)
@Composable
private fun PreviewItemCardBody2() {
    val data = TxnPendingListRes(
        txnId = "GD1122GD",
        amount = "***************",
        ccy = "VND",
        treasuryCode = "711",
        treasuryName = "Kho bạc nhà nước Việt Nam",
        createdDate = "30/10/2024",
        status = TransactionStatusBase.REJECTED.statusCode,
        statusName = stringResource(TransactionStatusBase.REJECTED.statusResourceId)
    )

    IBankDataCard(
        modifier = Modifier.padding(horizontal = IBSpacing.spacingM),
        showCheckbox = false,
        isChecked = false,
        onCheckedChange = {},
        cardHeader = {
            ItemCardHeader(data)
        },
        cardContent = {
            ItemCardBody(data)
        },
        cardFooter = {},
    )
}



